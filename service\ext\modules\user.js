import common from '@/common/util/main'
import api from '@/service/api'
import ext from '@/service/ext'
import constant from '@/constant'
import env from '@/config/env'
import validate from '@/common/util/validate'
import uniPlugin from '@/common/util/uni-plugin'
import md5 from 'js-md5'
import store from '@/store'
import navto from '@/router/config/nav-to'
import rsa from '@/common/util/rsa'
import { getQueryStr } from '@/utils/index'
import serverOptions from '@/config/env/options'
const launchOptions = uni.getLaunchOptionsSync()
/**
 * 针对获取数据进行二次数据清洗、加工、转化，特别指API
 */
console.log('api',api);
export default {
    /**
     * 清除本地会话
     */
    clearSession(callback) {
        const sessionArr = ['token', 'refreshToken', 'userAccount', 'tokenUuid', 'bannerList', 'unionid',
                'routerNameObj', 'storeList', 'curSelectStore', 'curSelectUserInfo', 'userInfoList',
                'curSelectUserInfoId', 'openId', 'curSelectStoreId', 'curSelectUnitId',
                'historyCount', 'isLogin'
            ] // 需要清除的存储数据
        sessionArr.forEach(k => {
            if (k) {
                common.setCache(k, '')
            }
        })
        store.dispatch('user/LoginOut') // 清除相关数据 clean
        store.dispatch('user/ClearPermissionsAll') // 清除权限数据
        store.dispatch('user/CleanWeChatInfo') // 清除微信信息 clean
        store.dispatch('user/UpdateRecordUserInfo', {}) // 清除档案信息 clean
        common.setKeyVal('system', 'systemMenuList', {}, true)
        common.setKeyVal('user', 'isLogin', false, true)
        common.setKeyVal('user', 'isBindWxAccountId', false, true)

        common.setKeyVal('chat', 'messageList', [], false)
        common.setKeyVal('chat', 'chatlist', [], false)
        common.setKeyVal('chat', 'messageInit', false, false)
        callback()
    },
    /**
     * 登出操作
     */
    loginOut(status,toastFlag = true) {
        const token = common.getToken()
        // 断开websocket
        ext.webSocket.webSocketIsReconnect = false
        ext.webSocket.webSocketPingTimer = null
        if (ext.webSocket.webSocket) {
            // #ifdef H5
            ext.webSocket.webSocket.close();
            // #endif

            // #ifndef H5
            ext.webSocket.webSocket.close({})
            // #endif
        }
        // ext.webSocket.webSocketSend(constant.chat.LOGIN_OUT_CMD,payload)
        // this.webSocketSend(constant.chat.LOGIN_OUT_CMD,payload);

        if (validate.isNull(token)) {
            uniPlugin.successToast('会话失效，重新登录！')
            navto.replaceAll('Login')
            return
        }
        const that = this
        const params = {}
        params.captcha = common.getTokenUuid()
        api.user.loginOut(params).then((res) => {
            that.clearSession(() => {
                toastFlag && uniPlugin.successToast('退出成功！')
                that.timer = setTimeout(() => {
                    navto.replaceAll('Login')
                }, 1000)
            })
        }).catch(error => {
            console.log(error)
            toastFlag && uniPlugin.successToast('退出失败！')
        })
    },
    /**
     * 组合获取业务-页面二次封装
     * @param that
     * @param callback
     */
    getInfoGroupPage(that, callback) {
        setTimeout(function() {
            that.$uniPlugin.loading('初始化档案中')
            that.$ext.user.getInfoGroup(() => {
                callback()
                let path = that.formPage || 'AccompanyHome'
                if (that.formPage && that.formPage === 'NotFind') { path = 'AccompanyHome' }
                let queryParams = {}
                if (!that.$validate.isNull(that.formPageParams)) {
                  console.log("formPageParams",that.formPageParams)
                    try {
                        // #ifndef H5
                        queryParams = JSON.parse(decodeURIComponent(decodeURIComponent(that.formPageParams)))
                        // #endif
                        // #ifdef H5
                        queryParams = that.formPageParams
                        // #endif
                    } catch (err) {
                        console.log('err--------------', err)
                    }
                }
                console.log("redirect:",that.formPage)
                console.log("queryParams:",queryParams)
                that.$uniPlugin.hideLoading()
                if(queryParams && queryParams instanceof Object && queryParams.isBackVisible) {
                  that.$navto.back(1)
                }else {
                  // that.$navto.replaceAll(path, that.codeFormParams || queryParams)
                  that.$navto.replaceAll(path, queryParams)
                }
            }, () => {
                callback()
                that.$uniPlugin.hideLoading()
                    // 没有数据
                that.$navto.pushTab('AccompanyHome')
            }, () => {
                callback()
                that.$uniPlugin.hideLoading()
                common.setKeyVal('user', 'token', '', true)
                that.$uniPlugin.toast('网络异常，请重试')
            })
        }, 1000)
    },
    /**
     * 组合获取业务用户信息
     */
    getInfoGroup(normalCallback, noDataCallback, errorCallback) {
      const that = this
      that.getTenantRecord().then((result) => {
        // debugger
        // if (result.length > 0 ) {
          that.getBusinessIdentity().then((result) => {
            that.getPermission().then((result) => {
              that.getRecordUserInfo().then((result) => {
                that.menuList(function (res) {
                })
                normalCallback()
              }).catch(e=>{
                errorCallback(e)
              })
            }).catch(e => {
              errorCallback(e)
            })
          }).catch(e => {
            errorCallback(e)
          })
        // } else {

        //   noDataCallback()
        // }
      }).catch(e => {
        errorCallback(e)
      })
    },
    /**
     * 对账号-登录信息进行处理
     *
     * @param {Object} name
     * @param {Object} pwd
     * @param {Object} loginType 登陆方式 1.手动登陆； 2.自动登陆（默认）
     */
    login(name, pwd, loginType) {
        return new Promise((resolve, reject) => {
            loginType = loginType || false
            let params = {}
                //todo 需要加RSA
                // if (loginType) {
                //   pwd = md5(pwd)
                // }
            params = {
                username: name, // 登陆账户名或手机号
                // pwd: escape(common.jsEncryptCode(md5(pwd))) // 登陆密码（经过md5处理过的）
                password: encodeURIComponent(rsa.jsEncryptCode(pwd))
            }
            params.captcha = common.getTokenUuid()
            api.user.login(params).then((res) => {
                res.loginStatus = true
                const data = res.data
                // 先给个中央用户id 防止没有档案拿不到中央用户id
                common.setKeyVal('user', 'curSelectUserInfo', { centerUserId: data.loginId}, true)
                common.setKeyVal('user', 'codeUserInfo', { id: data.loginId }, true)
                // #ifdef MP-WEIXIN
                ext.user.loginBindWeixinAccount()
                // #endif

                common.setKeyVal('user', 'token', data.tokenValue, true)
                    // common.setKeyVal('user', 'refreshToken', data.refreshToken, true)
                common.setKeyVal('user', 'isLogin', true, true)
                    // that.getCodeUserInfo()
                resolve(res)
            }).catch(error => {
                reject(error)
            })
        })
    },
  /**
   * 对账号-登录信息进行处理
   */
  register(params, resolve, reject) {
    const that = this
    api.user.register(params).then(res => {
      res.loginStatus = true
      const data = res.data
      common.setKeyVal('user', 'token', data.tokenValue, true)
      // common.setKeyVal('user', 'refreshToken', data.refreshToken, true)
      common.setKeyVal('user', 'isLogin', true, true)
      // that.getCodeUserInfo()
      resolve(res)
    }).catch(error => {
      // uniPlugin.toast(error.message)
      reject(error)
    })
  },
    /**
     * 对验证码-登录信息进行处理
     *
     * @param {Object} name
     * @param {Object} pwd
     * @param {Object} loginType
     */
    codeLogin(params) {
        const that = this
        return new Promise((resolve, reject) => {
            api.user.codeLogin(params).then(res => {
                res.loginStatus = true
                const data = res.data
              common.setKeyVal('user', 'token', data.tokenValue, true)
              // common.setKeyVal('user', 'refreshToken', data.refreshToken, true)
              common.setKeyVal('user', 'isLogin', true, true)
              // that.getCodeUserInfo()
              //   that.getCodeUserInfo()
                resolve(res)
            }).catch(error => {
                uniPlugin.toast(error.message)
                    // that.loginOut()
                reject(error)
            })
        })
    },
    /**
     * 获取资源权限
     * @param type
     */
    getPermission() {
        return new Promise((resolve, reject) => {
            const curSelectStoreId = common.getKeyVal('user', 'curSelectStoreId', true)
            const params = {
                // systemId: constant.noun.systemId,
                tenantId: curSelectStoreId
            }
            api.user.getPermission(params).then(res => {
                store.commit('user/SET_PERMISSIONS', res)
                resolve(res)
            }).catch(error => {
                reject(error)
            })
        })
    },
    /**
     * 通用获取产品用户档案信息
     * @param type
     */
    getRecordUserInfo() {
      const that = this
      return new Promise((resolve, reject) => {
        const curSelectStoreId = common.getKeyVal('user', 'curSelectStoreId', true)
        const params = {
          // systemId: constant.noun.systemId,
          tenantId: curSelectStoreId
        }
        //操作人类型：1-员工，2-医师，3-专员，4-患者 -userType
        let url = ''
        if (env.userType === 1) {
          url = ''
        } else if (env.userType === 2) {
          url = 'queryOnePhysician'
        } else if (env.userType === 3) {
          url = 'queryOneAttache'
        } else if (env.userType === 4) {
          url = 'queryOnePatient'
        }
        api.record[url](params).then(res => {
          if (Array.isArray(res.data.headPath) && res.data.headPath.length > 0) {
            res.data.headPath =  env.static_ctx + res.data.headPath
          } else if (res.data) {
            res.data.headPath =  env.static_ctx + (res.data.gender === 1?'image/business/icon-adult-boy.png':'image/business/icon-adult-girl.png')
          }
          store.commit('user/UPDATE_RECORDUSERINFO', res.data)
          resolve()
        }).catch(err=>{
          reject()
        })

      })
    },
    /**
     * 获取当前用户下该系统所有档案租户
     */
    getTenantRecord() {
        return new Promise((resolve, reject) => {
            api.user.getTenantRecord({userTenantRecordType:env.userType,tenantId:env.tenantId}).then((storeList) => {
                storeList = storeList.data
                const dataArr = storeList || []
                if (dataArr.length > 0) {
                    for (const obj of dataArr) {
                        obj.select = false
                    }
                    const curSelectStoreId = common.getKeyVal('user', 'curSelectStoreId', true)
                    let curSelectStore = null
                    let flag = false
                    for (const obj of dataArr) {
                        if (obj.tenantId == curSelectStoreId) {
                            obj.select = true
                            curSelectStore = obj
                            flag = true
                            break
                        }
                    }
                    // 若没能匹配到上次切换的租户，则选默认租户
                    if (!flag) {
                        for (let i = 0; i < dataArr.length; i++) {
                            if (i === 0) {
                                dataArr[i].select = true
                                curSelectStore = dataArr[i]
                                break
                            }
                        }
                    }
                    common.setKeyVal('user', 'storeList', dataArr, true)
                    common.setKeyVal('user', 'curSelectStore', curSelectStore, true)
                    common.setKeyVal('user', 'curSelectStoreId', curSelectStore.tenantId, true)
                    common.setKeyVal('user', 'curSelectUnitId', curSelectStore.tenantId, true)
                    resolve(storeList)
                } else {
                    common.setKeyVal('user', 'storeList', [])
                    resolve(storeList)
                }
            }).catch(error => {
                reject(error)
            })
        })
    },
    /**
     * 获取系统业务档案接口
     */
    getBusinessIdentity() {
        return new Promise((resolve, reject) => {
            const curSelectStore = common.getKeyVal('user', 'curSelectStore', true)
            const param = {
              userTenantRecordType: env.userType,
              recordId: validate.isNull(curSelectStore) ? '' : curSelectStore.userTenantRecordList[0].recordId,
              tenantId: common.getKeyVal('user', 'curSelectStoreId', true),
              accountId: common.getKeyVal('user', 'accountId')
            }
            // 先给个中央用户id 防止没有档案拿不到中央用户id
            common.setKeyVal('user', 'curSelectUserInfo', { centerUserId: validate.isNull(curSelectStore) ? '' : curSelectStore.userTenantRecordList[0].userId}, true)
            common.setKeyVal('user', 'codeUserInfo', { id: validate.isNull(curSelectStore) ? '' : curSelectStore.userTenantRecordList[0].userId }, true)
            // #ifdef MP-WEIXIN
            ext.user.loginBindWeixinAccount()
            // #endif

            api.user.getBusinessIdentity(param).then((res) => {
                if (validate.isNull(res.data)) {
                    common.setKeyVal('user', 'curSelectUserInfoId', '', true)
                    common.setKeyVal('user', 'curSelectUserInfo', '', true)
                    common.setKeyVal('user', 'codeUserInfo', '', true)
                    return
                }
                common.setKeyVal('user', 'curSelectUserInfoId', res.data.userId, true)
                common.setKeyVal('user', 'codeUserInfo', { id: res.data.centerUserId }, true)
                common.setKeyVal('user', 'curSelectUserInfo', res.data, true)
                resolve(res.data)
            }).catch(error => {
                resolve(error)
                // resolve(res)
            })
        })
    },
    // 对用户信息进行处理
    getUserInfo() {
        return new Promise((resolve, reject) => {
            api.user.getUserInfo().then((userInfo) => {
                let curSelectRole = {}
                const userList = userInfo.data || []
                    // 判断档案数据是否为空
                if (userList.length > 0) {
                    for (const obj of userList) {
                        // 获取用户性别icon

                        if (obj.headImage && obj.headImage.indexOf('http') <= -1) {
                            obj.headImage = env.file_ctx + obj.headImage
                        }
                        common.getDefaultHeadImage(obj)
                    }
                    // 设置默认选中角色
                    const curSelectRoleId = common.getKeyVal('user', 'curSelectRoleId', true)
                    if (!validate.isNull(curSelectRoleId)) {
                        for (const obj of userList) {
                            if (obj.id === curSelectRoleId) {
                                obj.select = true
                                curSelectRole = obj
                            } else {
                                obj.select = false
                            }
                        }
                        // 匹配不上，则采用第一个角色（切换单位后）(在有职工账号前提下，第一个账号永远为职工)
                        if (validate.isNull(curSelectRole)) {
                            userList[0].select = true
                            curSelectRole = userList[0]
                        }
                    } else {
                        for (const obj of userList) {
                            if (obj.select) {
                                curSelectRole = obj
                                break
                            }
                        }
                    }
                    userInfo.data = userList
                    const info = userInfo
                    common.setKeyVal('user', 'userInfo', info)
                    resolve(curSelectRole)
                } else {
                    curSelectRole = {}
                    common.getDefaultHeadImage(curSelectRole)
                    common.setKeyVal('user', 'userInfo', userInfo)
                    resolve(userInfo)
                }
                common.setKeyVal('user', 'curSelectRole', curSelectRole)
                common.setKeyVal('user', 'curSelectRoleId', curSelectRole.id, 'localStorage')
            }).catch(error => {
                reject(error)
            })
        })
    },
    /**
     * 获得临时token
     */
    getToken() {
        return new Promise((resolve, reject) => {
            const stam = +new Date()
            const encrypted = 'Vb8Uk9N&Q2*zDbx$' + stam
            const params = {
                captcha: common.getTokenUuid(),
                timestamp: stam + '', // 当前的时间戳
                encrypted: md5(encrypted) // 密文（经过md5处理过的）
            }
            api.user.getToken(params).then((res) => {
                const data = res.result
                common.setKeyVal('user', 'isTemporary', true, true)
                common.setKeyVal('user', 'token', data.accessToken, true)
                common.setKeyVal('user', 'refreshToken', data.refreshToken, true)
                resolve(data)
            }).catch(error => {
                reject(error)
            })
        })
    },
    getCodeUserInfo() {
        return new Promise((resolve, reject) => {
            api.user.getCodeUserInfo().then((res) => {
                store.commit('user/UPDATE_CODECUSERINFO', res)
                ext.wechat.getUnionid()
                resolve(res)
            }).catch(e => {
                reject(e)
            })
        })
    },
    /**
     * 手机号码登录
     * @param phone
     * @returns {Promise<unknown>}
     */
    phoneLogin(phone) {
        const that = this
        return new Promise((resolve, reject) => {
            const params = {}
            params.captcha = common.getTokenUuid()
            params.phone = phone
            params.serviceCode = 'nursery_service'
            api.user.phoneLogin(params).then((res) => {
                res.loginStatus = true
                const data = res.result
                common.setKeyVal('user', 'token', data.accessToken, true)
                common.setKeyVal('user', 'refreshToken', data.refreshToken, true)
                common.setKeyVal('user', 'isLogin', true, true)
                that.getCodeUserInfo()
                resolve(res)
            }).catch(error => {
                reject(error)
            })
        })
    },
    /**
     * 处理菜单列表和权限匹配接口中间层
     * @returns {Promise<unknown>}
     */
    sysuserentryQueryList(fn = () => {}) {
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo')
        const curSelectUserInfo = common.getKeyVal('user', 'curSelectUserInfo')
        if (validate.isNull(curSelectUserInfo)) return
        const paramObj = {
            type: curSelectUserInfo.type && curSelectUserInfo.type.key === constant.noun.staffCode.toString() ? constant.noun.staffCode : constant.noun.parentsCode,
            userId: codeUserInfo.id
        }
        api.sys.sysuserentryQueryList(paramObj).then(res => {
            fn(res)
        })
    },
    /**
     * 去除重复
     * @returns {Promise<unknown>}
     */
    removeDuplicates(arr, field) {
        const hash = {}
        arr = arr.reduce((item, next) => {
            hash[next[field]] ? '' : hash[next[field]] = true && item.push(next)
            return item
        }, [])
        return arr || []
    },
    /**
     * 处理菜单列表和权限匹配
     * @returns {Promise<unknown>}
     */
    menuList(fn = () => {}) {
        const systemMenuList = ext.systemMenuList
        const systemMenuObj = {}
        for (const a in systemMenuList) {
            systemMenuObj[a] = {}
            systemMenuObj[a].code = systemMenuList[a].code
            systemMenuObj[a].name = systemMenuList[a].name
            systemMenuObj[a].isPermissions = systemMenuList[a].isPermissions
            systemMenuObj[a].isState = systemMenuList[a].isState
            const list = []
            for (const b in systemMenuList[a].list) {
                // if (permission[systemMenuList[a].list[b].code]) {
                list.push(systemMenuList[a].list[b])
                    // }
            }
            systemMenuObj[a].list = list
        }
        common.setKeyVal('system', 'systemMenuList', systemMenuObj || {}, true)
    },
    /**
     * 处理小程序静默授权信息
     * @returns {Promise<unknown>}
     * */
    getWxUserBindAccount ({appId = env.appId,secret = 1,jsCode}) {
      return new Promise (async (resole,reject) => {

        // #ifdef MP-WEIXIN
        // 获取静默授权信息
        const res = await api.user.getMiniCode2Session({appId,secret,jsCode}).catch(err => {
          reject(err)
        })
        const {openid,sessionKey,unionid} = res.data
        // #endif

        // #ifndef MP-WEIXIN
        const {openid,appId,unionid} = arguments[0]
        // #endif

        // 小程序用户绑定唯一用户 获取accountId
        const resp = await api.user.getWxUserBindAccount({type:4,openId:openid,appId,unionId:unionid}).catch(err => {
          reject(err)
        })

        common.setKeyVal('user', 'wxUserInfo', resp.data)
        if (validate.isNull(resp.data) || validate.isNull(resp.data.id) || resp.data.id === 'null') {
            uniPlugin.modal('','网络失联，请点击刷新页面', {
                showCancel: false, // 是否显示取消按钮，默认为 true
                cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
                cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
                confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
                confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
                fn: async (n) => {
                  if(n) {
                    await ext.user.getWxUserBindAccount({appId,secret,jsCode})
                  }
                }
            })
            reject()
            return
        }
        const {id} = resp.data
        common.setKeyVal('user','accountId', id)
        common.setKeyVal('user','accountId', id, true)
        common.setKeyVal('user','isBindWxAccountId', true,true)
        // 中央用户档案添加第三方微信档案 绑定唯一用户
        ext.user.bindWeixinAccount({}).then(response => {
          resole(response)
        }).catch(err => {
          resole(err)
        })
      })
    },
    /**
     * 绑定微信唯一用户
     * @returns {Promise<unknown>}
     * */
    bindWeixinAccount ({accountId, tenantId,userId}) {
      return new Promise((resole,reject) => {
        accountId = accountId || common.getKeyVal('user', 'accountId', true)
        tenantId = tenantId || common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
        const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true)
        userId = userId || codeUserInfo.id
        if(!tenantId || !userId) {
          reject()
          return
        }
        api.user.bindWeixinAccount({accountId,tenantId,userId}).then((res) => {
          resole(res)
        }).catch(err => {
          reject(err)
        })
      })
    },
    /**
     * 登录默认授权绑定唯一微信唯一用户
     */
    loginBindWeixinAccount () {
        // #ifdef MP-WEIXIN
        const accountId = common.getKeyVal('user', 'accountId', true)
        common.setKeyVal('user', 'accountId', accountId)
        const isBindWxAccountId = common.getKeyVal('user', 'isBindWxAccountId', true)
        if(validate.isNull(accountId)) {
            uni.login({
                provider: 'weixin',
                scopes: 'auth_base',
                success: (res) => {
                    const {code} = res
                    ext.user.getWxUserBindAccount({jsCode: code})
                },
                fail: (err) => {
                    console.log('微信授权失败',err)
                }
            })
        } else if (!validate.isNull(accountId) && !validate.isNull(isBindWxAccountId)) {
            ext.user.bindWeixinAccount({})
        }
        // #endif
    },
    /**
     * 获取社区粉丝档案
     */
    async getCommunityFansRecord () {
        const param = {
            accountId: common.getKeyVal('user', 'accountId')
        }
        const res = await api.community.fansrecordGetFansrecordByAccountid(param)
        common.setKeyVal('user', 'fansRecord', res.data)
    },
    /**
     * 社区粉丝授权头像昵称信息
     */
    authCommunityFansInfo () {
        return new Promise(async (resolve, reject) => {
            const fansRecord = common.getKeyVal('user', 'fansRecord')
            if (validate.isNull(fansRecord)) {
                // #ifdef MP-WEIXIN
                // if (uni.getUserProfile) {
                //     uni.getUserProfile({
                //         desc: '用于社区评论点赞头像昵称展示',
                //         success: async res => {
                //             let userInfo = res.userInfo
                //             // 上传微信头像
                //             const uploadRes = await api.common.attachmentUrlMediaUploadOssByUrl({
                //                 url: userInfo.avatarUrl,
                //                 groupId: '26000'
                //             })
                //             userInfo = {
                //                 ...userInfo,
                //                 headPath: uploadRes.data.dir
                //             }
                //             await ext.user.addFans(userInfo)
                //             ext.user.getCommunityFansRecord()
                //             resolve()
                //         },
                //         fail: err => {
                //             console.log(err)
                //             reject(err)
                //         }
                //     })
                // }
                await ext.user.addFans()
                // ext.user.getCommunityFansRecord()
                resolve()
                // #endif
            } else {
                resolve()
            }
            resolve()
        })
    },
    /**
     * 添加社区粉丝
     */
    async addFans () {
        const userInfo = common.getKeyVal('user', 'wxUserInfo') || {}
        const curSelectUserInfo = common.getKeyVal('user', 'curSelectUserInfo')
        const { centerUserId = '' } = curSelectUserInfo || {}
        const params = {
            headPath: userInfo.headPath,
            nickName: userInfo.nickname,
            source: 1, // 来源渠道:1-小程序，2-后台手动新增，3-后台批量生成
            type: 1, // 类型：1-真实用户，2-马甲用户,3-系统用户
            accountId: common.getKeyVal('user', 'accountId'),
            userId: centerUserId,
            appId: env.appId,
            openId:await ext.wechat.getOpenId()
        }
        const res = await api.community.fansrecordAddMinappSource(params)
        const res2 = await api.community.accompanyfansrecordAddMinappSource(params) //陪诊专用的粉丝接口
        common.setKeyVal('user', 'fansRecordOld', res.data)
        common.setKeyVal('user', 'fansRecord', res2.data)
        return Promise.resolve()
    },
    /**
     * 根据accountId 获取临时token
     */
    async accountLogin () {
        function errorFunc() {
            uniPlugin.modal('','网络失联，请点击刷新页面', {
              showCancel: false, // 是否显示取消按钮，默认为 true
              cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
              cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
              confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
              confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
              fn: async (n) => {
                if(n) {
                    ext.user.getAccountIdGroup().then(() => {
                        ext.user.accountLogin()
                    })
                }
              }
            })
        }
        let accountId = common.getKeyVal('user', 'accountId', true)
        if(launchOptions.scene === 1154) accountId = 0;
        console.myLog('accountId1',accountId);
        const tenantId = common.getKeyVal('user', 'curSelectStoreId', true)
        if (validate.isNull(accountId) || accountId == 'undefined' || accountId == 'null') {
            errorFunc()
            return
        }
        const res = await api.user.accountLogin({ accountId, tenantId: (tenantId && tenantId !== 'undefined') ? tenantId : env.tenantId }).catch(() => {
            errorFunc()
        })
        const data = res.data
        common.setKeyVal('user', 'token', data.tokenValue, true)
        common.setKeyVal('user', 'isLogin', false, true)
    },
    /**
     * 获取account
     */
    getAccountIdGroup (scene=1001) {
      console.myLog('运行getAccountIdGroup',scene);
        return new Promise((resolve, reject) => {
            try {
                const isBindWxAccountId = common.getKeyVal('user', 'isBindWxAccountId', true)
                const fansRecord = common.getKeyVal('user', 'fansRecord')
                let accountId = common.getKeyVal('user', 'accountId', true);
                console.myLog('accountId',accountId);
                if(scene === 1154){
                    accountId = 0;
                    common.setKeyVal('user', 'accountId', accountId)
                    return resolve()
                }
                common.setKeyVal('user', 'accountId', accountId)
                // #ifdef MP-WEIXIN
                if(validate.isNull(accountId)) {
                    uni.login({
                        provider: 'weixin',
                        scopes: 'auth_base',
                        success: (res) => {
                            const {code} = res
                            ext.user.getWxUserBindAccount({jsCode: code}).then(() => {
                                resolve()
                            }).catch(() => {
                                reject()
                            })
                        },
                        fail: (err) => {
                            console.log('微信授权失败',err)
                            reject()
                        }
                    })
                } else if (!validate.isNull(accountId) && !isBindWxAccountId) {
                    ext.user.bindWeixinAccount({}).then(() => {
                        resolve()
                    }).catch(() => {
                        resolve()
                    })
                } else if (!validate.isNull(fansRecord)) {
                    resolve()
                } else {
                    resolve()
                }
                // #endif

                // H5从路径上拿参数获取accountId
                // #ifdef H5
                console.log(window.location.host ,env.domain_ctx + window.location.hash)
                if (window.location.host.indexOf('hulu.') !== -1) {
                    window.location.href = env.domain_ctx + window.location.hash
                    return
                }
                accountId = getQueryStr('accountId') ? getQueryStr('accountId') : accountId
                const appid = getQueryStr('appid')
                const openid = getQueryStr('openid')
                const unionid = getQueryStr('unionid')
                const tenantId = getQueryStr('tenantId')
                if (tenantId) {
                    common.setKeyVal('user', 'curSelectStoreId', tenantId, true)
                }
                if (accountId) {
                    common.setKeyVal('user', 'accountId', accountId)
                    common.setKeyVal('user', 'accountId', accountId, true)
                    resolve()
                } else if(openid && appid && unionid) {
                    // 不存在accountId时调接口获取
                    ext.user.getWxUserBindAccount({openid, appid, unionid}).then(() => {
                        resolve()
                    }).catch(() => {
                        resolve()
                    })
                } else if (!validate.isNull(fansRecord)) {
                    resolve()
                } else {
                    const UA = navigator.userAgent.toLowerCase()
                    // 是否是微信浏览器并存在accountId
                    if(UA.indexOf('micromessenger') != -1 && !accountId && process.env.NODE_ENV !== 'development') {
                        api.common.wxGetCommonOauth2WrapUrl({ url: encodeURIComponent(window.location.href) }).then(res => {
                            window.location.href = res
                        })
                    }
                }
                // #endif

                // 支付宝小程序端拿userId填充accountId
                // #ifdef MP-ALIPAY
                my.getAuthCode({
                  scopes: 'auth_base',
                  success: async (authInfo) => {
                    const res = await api.user.alipayGetUserid({ code: authInfo.authCode })
                    common.setKeyVal('user', 'accountId', res.data)
                    common.setKeyVal('user', 'accountId', res.data, true)
                    resolve()
                  },
                  fail: () => {
                    reject()
                  }
                })
                // #endif

                // #ifndef MP-WEIXIN || H5 || MP-ALIPAY
                resolve()
                // #endif
            } catch (err) {
                console.log('err-------------------------', err)
                reject(err)
            }

        })
    },
    /**
     * 绑定病友圈用户并修改注册状态
     */
    async usertenantrecordBindFans() {
        const fansRecord = common.getKeyVal('user', 'fansRecordOld') || {}
        const { centerUserId = '' } = common.getKeyVal('user', 'curSelectUserInfo') || {}
        if (!centerUserId) return
        // 是否绑定
        const param = {
          fansId: fansRecord.id,
          userId: centerUserId,
          tenantId: common.getKeyVal('user', 'curSelectStoreId',true) || env.tenantId
        }
        await api.community.usertenantrecordBindFans(param)
        // 修改注册状态
        if (fansRecord.registerStatus === 2) {
          await api.community.fansrecordUpdateRegisterStatus({ accountId: common.getKeyVal('user', 'accountId', true) })
        }
    },
    /**
     * 获取粉丝绑定的中央用户档案
     */
    async getFansBindRecord() {
      const fansRecord = common.getKeyVal('user', 'fansRecord') || {}
      if (!fansRecord) return
      const res = await api.user.getFansBindRecord({ fansId: fansRecord.id, tenantId: common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId }).catch(() => Promise.resolve())
      return Promise.resolve(res.data)
  },
  // 陪诊映射表
  payFnMap:{
    '1':()=>api.pay.orderPay,
    '2':()=>api.pay.comboPay,
    '3':()=>api.pay.orderPay,
    '4':()=>api.pay.combineOrderPay,
  },
  // combo隐射表 true:是套餐订单，false：是普通陪诊订单
  typeMap:{
    '1':1, // 普通订单
    '2':2, // 套餐订单
    '3':3, // 保险订单
    '4':4, // 联合订单
  },
  async lklaccompanyQueryOrder(id,key,providerId){
    providerId = providerId || serverOptions.providerId
    let type = this.typeMap[key]
    const res = await api.accompanyDoctor.lklaccompanyQueryOrder({type,id,providerId})
    return Promise.resolve(res)
  },
  // 判断这条单是否已经被支付过
  async isPayedOrder(bizOrderNo,type,providerId){
    const allinpaydetailRes = await this.lklaccompanyQueryOrder(bizOrderNo,type,providerId);
    if (allinpaydetailRes?.data?.greenbonResp) {
      return allinpaydetailRes;
    }
    return false;
  },
  /**
   * 陪诊支付方法
   */
  async accompanyPay({bizOrderNo,successCb,failCb,type,productCode,accompanyCombineInsureList}){
    // 获取定义的支付方法
    let requeryPayFn = this.payFnMap[type]();
    uniPlugin.loading()
    // 判定该条订单是否已支付若该订单申请支付过则返回该订单旧的支付参数 这次返回会返回带下划线的参数
    const allinpaydetailRes = await this.isPayedOrder(bizOrderNo,type);
    let data = allinpaydetailRes?.data?.greenbonResp || {data:{}};
    let payInfo = (typeof(data) === 'string' ? JSON.parse(data) : data)?.resp_data?.acc_resp_fields;
    // 若未读到 则可以重新请求
    if(!allinpaydetailRes){
      const openId = await ext.wechat.getOpenId()
      let params = {
            subAppid:serverOptions.getoptions().appId,
            userId:openId,
            id:bizOrderNo,
            providerId:serverOptions.providerId,
            // 是否本地app 如果是本地app则传true 否则传false
            localApp:serverOptions.getoptions().appId === serverOptions.appId,
            productCode,
            accompanyCombineInsureList
          }
      try {
        // 请求支付参数 这次返回会返回驼峰式的参数
        const {data:{respData}} = await requeryPayFn(params)
        payInfo = typeof(respData.accRespFields) === 'string' ? JSON.parse(respData.accRespFields) : respData.accRespFields
      } catch (error) {
        return failCb && failCb(error)
      }
    }
    uniPlugin.loading('请求支付中...', true)
    // 调用微信支付
    wxPay(payInfo,successCb,failCb)

    async function wxPay(payInfo,successCb,failCb){
       // 暂时注释
      wx.requestPayment({
        timeStamp: payInfo.time_stamp || payInfo.timeStamp,
        nonceStr: payInfo.nonce_str || payInfo.nonceStr,
        package: payInfo.package || payInfo.package || payInfo.packageStr,
        signType: payInfo.sign_type || payInfo.signType,
        paySign: payInfo.pay_sign || payInfo.paySign,
        success: res => {
          setTimeout(() => {
              uniPlugin.toast('支付成功');
          }, 1000);
          successCb && successCb()
        },
        fail: err => {
          console.log('err',err);
          if(err.errMsg === "requestPayment:fail cancel")
          uniPlugin.toast('取消支付');
          failCb && failCb()
        },
        complete: res => {
          console.log('res',res);
          uniPlugin.hideLoading()
        }
      })
    }
  },

  /**
   * 陪诊师报名费支付方法
   * @param {String} id - 陪诊师ID或支付业务ID
   * @param {Function} successCb - 支付成功回调
   * @param {Function} failCb - 支付失败回调
   * @param {Boolean} isPayBusinessId - 是否为支付业务ID的标志
   * @param {String} employeeId - 当使用业务ID但需要创建新支付时的陪诊师ID
   */
  async accompanyEmployeeFeePay(id, successCb, failCb, isPayBusinessId = false, employeeId = null) {
    uniPlugin.loading('请求支付中...')

    try {
      let payInfo = null;

      // 如果是支付业务ID，查询历史订单
      if (isPayBusinessId) {
        console.log('使用支付业务ID查询历史订单记录');

        // 查询历史订单
        const allinpaydetailRes = await api.accompanyDoctor.lklaccompanyQueryOrder({
          type: 9,
          id: id,
          providerId: serverOptions.providerId
        });

        // 如果有历史支付记录，使用历史支付参数
        if (allinpaydetailRes && allinpaydetailRes.data && allinpaydetailRes.data.greenbonResp) {
          console.log('找到历史支付记录，使用历史支付参数');
          const data = allinpaydetailRes.data.greenbonResp;
          payInfo = (typeof(data) === 'string' ? JSON.parse(data) : data)?.resp_data?.acc_resp_fields;
        }
      }

      // 如果没有找到历史支付参数，请求新的支付参数
      if (!payInfo) {
        console.log('请求新支付参数');
        const openId = await ext.wechat.getOpenId();

        // 构建请求参数
        const params = {
          subAppid: serverOptions.getoptions().appId,
          userId: openId,
          providerId: serverOptions.providerId
        };

        // 根据ID类型设置不同的参数
        if (isPayBusinessId) {
          // 如果是业务ID但找不到历史订单，需要使用陪诊师ID创建新订单
          if (!employeeId) {
            uniPlugin.hideLoading();
            uniPlugin.toast('历史支付订单已过期，请返回重试');
            return failCb && failCb({ msg: '历史支付订单已过期，缺少陪诊师ID无法创建新订单' });
          }
          params.employeeId = employeeId; // 使用传入的陪诊师ID
        } else {
          params.employeeId = id; // 使用陪诊师ID
        }

        // 请求支付参数
        const result = await api.accompanyDoctor.lklaccompanyEmployeeFeePay(params);

        if (!result || result.code !== 0 || !result.data || !result.data.respData) {
          uniPlugin.hideLoading();
          uniPlugin.toast('获取支付参数失败');
          return failCb && failCb({ msg: '获取支付参数失败' });
        }

        // 解析支付参数
        const respData = result.data.respData;
        payInfo = typeof(respData.accRespFields) === 'string' ?
          JSON.parse(respData.accRespFields) : respData.accRespFields;
      }

      // 确保获取到支付参数后，调用微信支付
      if (!payInfo) {
        uniPlugin.hideLoading();
        uniPlugin.toast('获取支付参数失败');
        return failCb && failCb({ msg: '获取支付参数失败' });
      }

      // 调用微信支付，兼容两种格式的参数名称
      wx.requestPayment({
        timeStamp: payInfo.timeStamp || payInfo.time_stamp,
        nonceStr: payInfo.nonceStr || payInfo.nonce_str,
        package: payInfo.package || payInfo.packageStr,
        signType: payInfo.signType || payInfo.sign_type,
        paySign: payInfo.paySign || payInfo.pay_sign,
        success: res => {
          setTimeout(() => {
            uniPlugin.toast('支付成功');
          }, 1000);
          successCb && successCb();
        },
        fail: err => {
          console.log('err', err);
          if (err.errMsg === "requestPayment:fail cancel") {
            uniPlugin.toast('取消支付');
          }
          failCb && failCb(err);
        },
        complete: res => {
          console.log('res', res);
          uniPlugin.hideLoading();
        }
      });
    } catch (error) {
      uniPlugin.hideLoading();
      uniPlugin.toast('支付请求失败');
      failCb && failCb(error);
    }
  },
}
