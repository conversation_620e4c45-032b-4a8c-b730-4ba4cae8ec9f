<template>
  <view class='order-center'>
    <!-- <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack">
        <image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/>
      </view>
      <view class="top-nav-c">订单中心</view>
    </view> -->
    <view class="search">
      <!-- <search class="my-search" :isShow="false" :fixed="true" top="88" placeholder="搜索就诊人、陪诊师" v-model="search"></search> -->
      <view class="input-view">
        <i class="icon-positioning-search"></i>
        <input confirm-type="search" placeholder="搜索就诊人、陪诊师" placeholder-style="color: #A5AAB8" class="input"
          type="text" :value="search" @input="handleSearchInput" @confirm="handleSearch">
      </view>
      <view class="time-container" @click="showTimePicker">
        <view v-if="!showTimeMap[0] && !showTimeMap[1]" class="date-create">
          <text class="date-label">筛选日期</text>
        </view>
        <view v-else>
          <view class="date-range">
            <text class="date-label">起</text>
            <text class="date-value">{{ showTimeMap[0] }}</text>
          </view>
          <view class="date-range">
            <text class="date-label">止</text>
            <text class="date-value">{{ showTimeMap[1] }}</text>
          </view>
        </view>
      </view>
      <view class="date-image">
        <uni-icons type="bottom"></uni-icons>
      </view>
      <!-- 时间选择器组件 -->
      <timePicker ref="selectTime" :value="timeMap" type="daterange" @change="handleTimeChange" :show="isPickerVisible"
        @cancel="timePickerVisible = false"></timePicker>
    </view>
    <view class="l-main">
      <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" :overflowY="false" v-model="curIndex" :tabs="tabs"
        @change="changeTab"></tabs-sticky>
    </view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption"
      :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="order-list">
        <view class="order-item" v-for="item in contentList" :key="item.id"
          @click="$navto.push('accompanyDetails',{id:item.id,isServer:true})">
          <view class="order-item-t">
            <view class="order-item-t-l">{{ item.serviceName }}</view>
            <view class="order-item-t-r"
              :style="{color:item.orderState == 1 ? '#1687F7' : item.orderState == 2 ? '#FF5500' : (item.orderState == 5 || item.orderState == 6) ? '#00B484' : item.orderState == 7 ? '#1D2029' : '#868C9C'}">
              {{ handleFilterOrderState(item.orderState) }}
            </view>
          </view>
          <view class="order-item-b">
            <view class="cost" v-if="item.bookName">就诊人：<span>{{ item.bookName }}</span></view>
            <view class="cost" v-if="item.payPrice">服务费用：<span>￥{{ item.payPrice / 100 }}</span></view>
            <view class="time">陪诊时间：<span>{{ item.startTime }}~{{ item.endTime }}</span></view>
            <view class="hospital">就诊医院：<span>{{ item.hospitalName }}</span></view>
            <view class="hospital">订单号：<span>{{ item.id }}</span></view>
            <view class="accompany-teacher" v-if="item.employeeId">陪诊师：<view class="teacher-box">
                <view class="img"><image class="img" :src="file_ctx + item.avatar"></image></view> <span>{{ item.employeeName }}</span>
              </view>
            </view>
            <!-- <button class="evaluate" v-if="item.orderState === 7 && item.commentState !== 2">前往评价</button> -->
            <view class="my-evaluate" v-if="item.commentState == 2">
              用户评价：
              <view class="evaluate-img1" v-if="item.star == 1">
                <image class="img"
                  :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'"></image>
              </view>
              <view class="evaluate-img2" v-if="item.star == 2">
                <image class="img"
                  :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'"></image>
              </view>
              <view class="evaluate-img3" v-if="item.star == 3">
                <image class="img"
                  :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'"></image>
              </view>
              <view class="evaluate-img4" v-if="item.star == 4">
                <image class="img"
                  :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image>
              </view>
              <view class="evaluate-img5" v-if="item.star == 5">
                <image class="img"
                  :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'"></image>
              </view>
            </view>
          </view>
        </view>

      </view>
    </scroll-refresh>
    <view class="work-bench-bott" @click="$navto.push('accompanyCreateOrder')"><button>
        <view class="img">
          <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-add-order.png'"></image>
        </view>创建订单
      </button></view>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  // import search from '../../components/search'
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker'
  import UniIcons from '@/components/uni/uni-icons/uni-icons'
  export default {
    components: {
      TabsSticky,
      // search,
      timePicker,
      UniIcons,
    },
    data(){
      return{
        $navto:this.$navto,
        file_ctx:this.file_ctx,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        statusBarHeight:0,
        curIndex:0,
        tabs:[{name:'全部',id:null},{name:'待接入',id:1},{name:'待支付',id:2},{name:'待派单',id:3},{name:'待接单',id:4},{name:'待服务',id:5},{name:'服务中',id:6},{name:'已完成',id:7},{name:'已取消',id:8}],
        orderState:null,
        navCurrent:0,
        tabCurrent:0,
        search:'',
        searchTimer: null,    // 防抖计时器
        debounceTime: 500,    // 防抖延迟时间
        timeMap: [],
        showTimeMap: [],
        timePickerVisible: false
      }
    },
    onLoad(){
    },
    onShow() {
      this.refreshOrderList(true)
    },
    created() {
      // 初始化默认时间范围
      this.initDefaultDate()
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      // 初始化默认日期
      initDefaultDate() {
        this.timeMap = []; // 默认不设置时间范围
        this.showTimeMap = []; // 初始显示为空
      },
      // 日期格式化方法（兼容Uniapp）
      formatDate(date) {
        if (!(date instanceof Date)) return ''
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        return `${year}-${month}-${day}`
      },
      // 时间选择器回调
      handleDateConfirm(e) {
        const [start, end] = e.detail.value
        // 更新存储值
        this.timeMap = [start, end]
        // 更新显示值
        this.showTimeMap = [
          start.replace(/-/g, '.'),
          end.replace(/-/g, '.')
        ]
        // 触发数据刷新
        this.refreshOrderList()
      },
      formatDateTime(date, isEnd = false) {
        if (!(date instanceof Date)) return ''
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')

        // 根据是否是结束时间设置时分秒
        if (isEnd) {
          return `${year}-${month}-${day} 23:59:59`
        } else {
          return `${year}-${month}-${day} 00:00:00`
        }
      },
      // 显示时间选择器
      showTimePicker() {
      // 确认日期选择
        this.timePickerVisible = true
        this.$nextTick(() => {
          this.$refs.selectTime && this.$refs.selectTime.show()
        })
      },
      handleTimeChange(timeRange) {
        if (timeRange && timeRange.length === 2) {
          this.timeMap = timeRange;
          this.showTimeMap = [
            timeRange[0].split(' ')[0].replace(/-/g, '.'),
            timeRange[1].split(' ')[0].replace(/-/g, '.')
          ];
        } else {
          // 清空选择时重置为全部时间
          this.timeMap = [];
          this.showTimeMap = [];
        }
        this.refreshOrderList(true);
      },
      // 输入事件处理
      handleSearchInput (e) {
        const keyword = e.detail.value;
        this.search = keyword; // 同步输入值
        if (this.searchTimer) clearTimeout(this.searchTimer);
        this.searchTimer = setTimeout(() => {
          this.refreshOrderList(true);
        }, this.debounceTime);
      },
      // 手动触发搜索（回车/按钮）
      handleSearch () {
        if (this.searchTimer) clearTimeout(this.searchTimer);
        this.refreshOrderList(true);
      },
      // 统一刷新方法（支持分页重置）
      refreshOrderList(isSearch = false) {
        if (isSearch) {
          // 搜索时重置到第一页
          this.mescroll && this.mescroll.triggerDownScroll(true);
        } else {
          // 普通刷新保持当前页
          this.mescroll && this.mescroll.refresh();
        }
      },
      formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      },

      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
          lastMonth.setFullYear(lastMonth.getFullYear() - 1);
        }

        lastMonth.setDate(1);
        dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd'))
        dates.push(this.$common.formatDate(today, 'yyyy-MM-dd'))
        return dates;
      },
      changeTime(){
        this.refreshOrderList(); // 改为直接刷新
      },
      handleClickJump(index){
        this.navCurrent = index
      },
      handleFilterOrderState(type){
        switch(type){
          case 1:
            return '等待人工导诊'
          case 2:
            return '待支付'
          case 3:
            return '待派单'
          case 4:
            return '待接单'
          case 5:
            return '待服务'
          case 6:
            return '服务中'
          case 7:
            return '已完成'
          case 8:
            return '已取消'
        }
      },
      returnFn(obj = { pageNum: 1, pageSize: 10 }) {
        const that = this;
        const { centerUserId: userId = '' } = this.curSelectUserInfo || {};
        let params = {
          current: obj.pageNum || 1,
          size: obj.pageSize || 10,
          condition: {
            orderState: this.orderState,
            searchName: this.search,
            startStartTime: this.timeMap[0] || null,
            endStartTime: this.timeMap[1] || null,
            // userId:userId,
          }
        }
        that.$api.accompanyDoctor.accompanybookQuery(params).then(res => {
          let data = res.data.records.map(item=>(
            {
              ...item,
              startTime:that.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd').replace(/-/g, '.'),
              endTime: item.endTime ? that.$common.formatDate(new Date(item.endTime), 'yyyy-MM-dd').replace(/-/g, '.') : '',
            }))
          if (obj.pageNum === 1) {
            that.contentList = []
          }
          that.contentList = [...that.contentList, ...data]
          obj.successCallback && obj.successCallback(data)
        }).catch(err => {
          obj.errorCallback && obj.errorCallback(err)
        })
      },

      scrollInit(scroll) {
        this.mescroll = scroll;
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
      },
      init() {
        this.$nextTick(() => {
          if (this.mescroll) {
            this.mescroll.triggerDownScroll()
          }
        })
      },
      changeTab(index) {
        this.tabCurrent = index
        this.orderState = this.tabs[index].id
        this.init()
      },
      handleBack(){
        this.$navto.back(1)
      },
    },
 }
</script>

<style lang='scss' scoped>
  .order-center{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #F4F6FA;
    .scroll-refresh-main{
      flex: 1;
      // height: calc(100% - 60rpx);
      overflow-x: hidden;
      padding-bottom: 166rpx;
      background-color: #F4F6FA;
      ::v-deep .mescroll-empty-box{
        // min-height: 0%;
        position: absolute !important;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .order-list{
        overflow-x: hidden;
        margin: 0 32rpx;
        border-radius: 16rpx;
        .order-item{
          padding:32rpx 24rpx;
          margin-top: 20rpx;
          background-color: #fff;
          border-radius: 16rpx;
          .order-item-t{
            display: flex;
            justify-content: space-between;
            .order-item-t-l{
              font-size: 32rpx;
              color: #1D2029;
              line-height: 44rpx;
            }
            .order-item-t-r{
              font-size: 28rpx;
              color: #1687F7;
              line-height: 40rpx;
            }
          }
          .order-item-b{
            margin-top: 16rpx;
            .cost,.time,.hospital,.accompany-teacher{
              font-size: 24rpx;
              color: #4E5569;
              line-height: 34rpx;
              span{
                color: #1D2029;
              }
            }
            .time{
              margin: 8rpx 0;
            }
            .hospital{}
            .accompany-teacher{
              display: flex;
              .teacher-box{
                display: flex;
                align-items: center;
                .img{
                  width: 32rpx;
                  height: 32rpx;
                  background-color: pink;
                  border-radius: 50%;
                }
                span{
                  margin-left: 8rpx;
                  font-size: 22rpx;
                  color: #1D2029;
                  line-height: 32rpx;
                }
              }
            }
            .evaluate{
              display: flex;
              align-items: center;
              justify-content: center;
              height: 72rpx;
              width: 100%;
              margin-top: 24rpx;
              font-size: 26rpx;
              color: #00B484;
              background: #FFFFFF;
              border-radius: 36rpx;
              border: 1rpx solid #00B484;
              &::after{
                border: none !important;
              }
            }
            .my-evaluate{
              display: flex;
              align-items: center;
              height: 72rpx;
              background: #F4F6FA;
              border-radius: 8rpx;
              padding: 18rpx 16rpx 20rpx;
              box-sizing: border-box;
              margin-top: 24rpx;
              // .evaluate-img{
              //   display: flex;
              //   width: 132rpx;
              //   height: 32rpx;
              // }
              .evaluate-img1{
                width: 204rpx;
                height: 48rpx;
              }
              .evaluate-img2{
                width: 156rpx;
                height: 48rpx;
              }
              .evaluate-img3{
                width: 132rpx;
                height: 48rpx;
              }
              .evaluate-img4{
                width: 132rpx;
                height: 48rpx;
              }
              .evaluate-img5{
                width: 180rpx;
                height: 48rpx;
              }
            }
          }
        }
      }
    }
  }
  .white-space{
    white-space: nowrap;
    display: flex;
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .l-main{
    position: sticky;
    top: 0;
    // z-index: 999;
    padding: 22rpx 32rpx 22rpx 38rpx;
    background-color: #fff;
    ::v-deep.tabs-sticky{
      overflow-y: hidden;
      .tabs-sticky-body{
        padding: 0;
        .tab{
          text{
            padding: 0;
          }
          &:last-child{
            padding-right: 50rpx;
          }
        }
      }
    }
  }
  .search{
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    background: #fff;
    .input-view {
      display: flex;
      align-items: center;
      vertical-align: middle;
      // width: calc(100% - 220upx);
      width: 466rpx;
      // @include rounded(38upx);
      line-height: 72upx;
      height: 72upx;
      padding: 0 20upx;
      background: #FFFFFF;
      border-radius: 36rpx;
      border: 2rpx solid #D9DBE0;
      .icon-positioning-search{
        display: inline-block;
        vertical-align: middle;
        margin-right: 6upx;
        @include iconImg(32, 32, '/system/icon-positioning-search.png');
      }
      .input {
        width: calc(100% - 78upx);
        display: inline-block;
        vertical-align: middle;
        font-size: 28upx;
        line-height: 42upx;
        color: #333;
      }
    }
    // ::v-deep.my-search{
    //   .search{
    //     width: 518rpx;
    //   }
    // }
    .lineHide{
      margin-left: 32rpx;
      .time-hide{
        width: 0;
        overflow: hidden;
        height: 0;
      }
      .text{
        display: flex;
        align-items: center;
        image{
          display: flex;
          height: 24rpx;
          width: 24rpx;
          margin-left: 6rpx;
        }
      }
      ::v-deep.uni-date{
        display: flex;
        flex: 1;
        .uni-date-editor{
          .uni-date-editor--x{
            height: 64rpx;
            border-radius: 32rpx;
            background: #fff;
            overflow: hidden;
            border: none;
            .uni-date-x{
              flex: none;
              padding-left: 32rpx;
              .range-separator{
                margin: 0 10rpx;
              }
              .uni-date__x-input{
                flex: none;
              }
            }
          }
        }
      }
    }
  }

  // .top-nav{
  //   // position: fixed;
  //   width: calc(100% - 16rpx);
  //   display: flex;
  //   align-items: center;
  //   justify-content: space-between;
  //   height: 40px;
  //   line-height: 40px;
  //   padding: 0 32rpx;
  //   // z-index: 999;
  //   // padding: 0 32rpx 0 24rpx;
  //   font-size: 32rpx;
  //   color: #FFFFFF;
  //   .top-nav-l{
  //     display: flex;
  //     width: 48rpx;
  //     height: 48rpx;
  //     image{
  //       width: 100%;
  //       height: 100%;
  //     }
  //   }
  //   .top-nav-c{
  //     flex: 1;
  //     text-align: center;
  //     height: 44rpx;
  //     font-weight: 500;
  //     font-size: 32rpx;
  //     color: #2D2F38;
  //     line-height: 44rpx;
  //     margin-right: 48rpx;
  //   }
  // }
  .work-bench-bott{
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 160rpx;
    background: #FFFFFF;
    box-sizing: border-box;
    padding: 20rpx 32rpx 0;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    button{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-size: 32rpx;
      color: #FFFFFF;
      .img{
        display: flex;
        width: 48rpx;
        height: 48rpx;
        image{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  /* 时间容器 */
  .time-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* margin-left: 24rpx; */
  }
  /* 日期范围 */
  .date-range {
    display: flex;
    align-items: center;
    padding: 8rpx 12rpx;
    border-radius: 8rpx;
  }
  .date-label {
    color: #666;
    font-size: 28rpx;
    margin: 0 4rpx;
  }
  .date-value {
    color: #333;
    font-size: 28rpx;
    margin: 0 8rpx;
  }
  .date-create {
    margin-left: 16rpx ;
  }
  ::v-deep .uni-date-x {
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
  }
  ::v-deep .uni-date {
    height: 0 !important;
    width: 0 !important;
    overflow: hidden !important;
  }
</style>
