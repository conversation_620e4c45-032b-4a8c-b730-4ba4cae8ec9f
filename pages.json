{
    "pages": [
      {
        "path": "pages/accompany-home/index",
        "style": {
          "navigationBarTitleText": "首页",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "none",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/service/index",
        "style": {
          "navigationBarTitleText": "服务",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "none",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/order/index",
        "style": {
          "navigationBarTitleText": "订单",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "none",
            "titlePenetrate": "YES"
          }
        }
      },
      {
        "path": "pages/my/index",
        "style": {
          "navigationBarTitleText": "我的",
          "navigationStyle": "custom",
          "app-plus": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-weixin": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "h5": {
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false
          },
          "mp-alipay": {
            "allowsBounceVertical": "NO",
            // 将回弹属性关掉
            "bounce": "none",
            "titleNView": false,
            "transparentTitle": "none",
            "titlePenetrate": "YES"
          }
        }
      }

    ],
    "subPackages": [
    {
        "root": "modules/accompany-doctor",
        "pages": [
            {
                "path": "distribute/index",
                "style": {
                    "navigationBarTitleText": "申请分销",
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "storeManagement/index",
                "style": {
                    "navigationBarTitleText": "门店管理",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "storeManagement/fixtures",
                "style": {
                    "navigationBarTitleText": "一键装修",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "server/withdraw",
                "style": {
                    "navigationBarTitleText": "提现",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "server/balances",
                "style": {
                    "navigationBarTitleText": "我的余额",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },

            {
                "path": "combo-detail/index",
                "style": {
                    "navigationBarTitleText": "套餐详情",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "combo-order/index",
                "style": {
                    "navigationBarTitleText": "套餐订单",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "my-combo/index",
                "style": {
                    "navigationBarTitleText": "订单",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "home/index",
                "style": {
                    "navigationBarTitleText": "优德医陪诊",
                    "enablePullDownRefresh": false,
                    "navigationBarBackgroundColor": "#F4F6FA",
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "home/components/personal/my-collect/index",
                "style": {
                    "navigationBarTitleText": "我的收藏",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "home/components/personal/my-posts/index",
                "style": {
                    "navigationBarTitleText": "我的帖子",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "home/components/personal/my-comment/index",
                "style": {
                    "navigationBarTitleText": "我的评论",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "home/components/personal/my-like/index",
                "style": {
                    "navigationBarTitleText": "我的点赞",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "home/components/news/like-collect/index",
                "style": {
                    "navigationBarTitleText": "点赞与收藏",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "home/components/news/reply/index",
                "style": {
                    "navigationBarTitleText": "我的回复",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/home/<USER>",
                "style": {
                    "navigationBarTitleText": "陪诊师首页",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "accompany/home/<USER>",
                "style": {
                    "navigationBarTitleText": "陪诊师详情",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "accompany/home/<USER>",
                "style": {
                    "navigationBarTitleText": "创建订单",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "accompany/home/<USER>",
                "style": {
                    "navigationBarTitleText": "海报",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "accompany/business-target/index",
                "style": {
                    "navigationBarTitleText": "业务指标",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/data-panel/index",
                "style": {
                    "navigationBarTitleText": "",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/work-bench/index",
                "style": {
                    "navigationBarTitleText": "",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/order-center/index",
                "style": {
                    "navigationBarTitleText": "订单中心",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/update-data/index",
                "style": {
                    "navigationBarTitleText": "资料修改",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/update-data/update-user-info",
                "style": {
                    "navigationBarTitleText": "资料修改",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/index",
                "style": {
                    "navigationBarTitleText": "课程",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/course-detail",
                "style": {
                    "navigationBarTitleText": "课程详情",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/second-classify",
                "style": {
                    "navigationBarTitleText": "二级分类名称",
                    "navigationBarBackgroundColor": "#F4F6FA",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/course-search",
                "style": {
                    "navigationBarTitleText": "课程搜索",
                    "navigationBarBackgroundColor": "#F4F6FA",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/recently-study",
                "style": {
                    "navigationBarTitleText": "最近学习",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "accompany/course/study-statement",
                "style": {
                    "navigationBarTitleText": "学习报表",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/index",
                "style": {
                    "navigationBarTitleText": "陪诊服务",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/insuranceList",
                "style": {
                    "navigationBarTitleText": "门诊无忧服务订单",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"

                        }
                    }
                }
            },
            {
                "path": "service-reservation/add-patient/index",
                "style": {
                    "navigationBarTitleText": "添加就诊人",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/add-patient/add",
                "style": {
                    "navigationBarTitleText": "新增就诊人",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/joinOrder/joinOrder",
                "style": {
                    "navigationBarTitleText": "联合订单详情",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/add-patient/edit",
                "style": {
                    "navigationBarTitleText": "编辑就诊人",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/accompany-record/index",
                "style": {
                    "navigationBarTitleText": "陪诊记录",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/instruction",
                "style": {
                    "navigationBarTitleText": "服务细则",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "service-reservation/service-message/index",
                "style": {
                    "navigationBarTitleText": "服务信息",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "system-search/index",
                "style": {
                    "navigationBarTitleText": "搜索",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            },
            {
                "path": "webView/webView",
                "style": {
                    "navigationBarTitleText": "外部网页",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            },
            {
                "path": "system-search/search-data",
                "style": {
                    "navigationBarTitleText": "搜索",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            },
            {
                "path": "system-search/accompany-teacher",
                "style": {
                    "navigationBarTitleText": "社区交流",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "system-search/doctor-list",
                "style": {
                    "navigationBarTitleText": "本地名医",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "system-search/accompany-list",
                "style": {
                    "navigationBarTitleText": "陪诊师",
                    "enablePullDownRefresh": false,
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "system-search/accompany-details",
                "style": {
                    "navigationBarTitleText": "陪诊师",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            },
            {
                "path": "system-search/publish-post",
                "style": {
                    "navigationBarTitleText": "",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "service-detail/index",
                "style": {
                    "navigationBarTitleText": "",
                    "enablePullDownRefresh": false,
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#F4F6FA"
                        }
                    }
                }
            }
        ]
    },
    {
        "root": "modules/system",
        "pages": [
            {
                "path": "login/index",
                "style": {
                    "navigationBarTitleText": "用户登录",
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            },
            {
                "path": "modify-phone/index",
                "style": {
                    "navigationBarTitleText": "修改手机号码",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "agreement-gather/user-agreement/index",
                "style": {
                    "navigationBarTitleText": "服务协议",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
              "path": "agreement-gather/order-agreement/index",
              "style": {
                  "navigationBarTitleText": "下单协议",
                  "app-plus": {
                      "titleNView": {
                          "backgroundColor": "#fff"
                      }
                  }
              }
          },
            {
              "path": "agreement-gather/arrival-agreement/index",
              "style": {
                  "navigationBarTitleText": "陪诊师入驻协议",
                  "app-plus": {
                      "titleNView": {
                          "backgroundColor": "#fff"
                      }
                  }
              }
          },
            {
                "path": "agreement-gather/exeception-clause/index",
                "style": {
                    "navigationBarTitleText": "服务协议",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
              "path": "application/index",
              "style": {
                  "navigationBarTitleText": "陪诊师申请",
                  "app-plus": {
                      "titleNView": {
                          "backgroundColor": "#fff"
                      }
                  }
              }
          },
          {
            "path": "application/intro",
            "style": {
                "navigationBarTitleText": "陪诊师申请须知",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
          },
            {
                "path": "agreement-gather/secrecy-policy/index",
                "style": {
                    "navigationBarTitleText": "隐秘政策",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            }
        ]
    },
    {
        "root": "modules/activity",
        "pages": [
            {
                "path": "hospital-ranking/index",
                "style": {
                    "navigationBarTitleText": "医院排行",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            },
            {
                "path": "hospital-ranking/hospital-detail/index",
                "style": {
                    "navigationBarTitleText": "医院详情",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "hospital-ranking/doctor-detail/index",
                "style": {
                    "navigationBarTitleText": "医生详情",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            },
            {
                "path": "pages/diagnosis/list",
                "style": {
                    "navigationBarTitleText": "诊后点评",
                    "navigationStyle": "custom",
                    "app-plus": {
                        "titleNView": {
                            "backgroundColor": "#fff"
                        }
                    }
                }
            }
        ]
    },
    {
        "root": "modules/community",
        "pages": [
            {
                "path": "posts/detail/index",
                "style": {
                    "navigationBarTitleText": "帖子详情",
                    "app-plus": {
                        "titleNView": false
                    }
                }
            }
        ]
    },
    {
        "root": "modules/distribute",
        "pages": [
        ]
    },
    {
      "root": "modules/distribution",
      "pages": [
        {
          "path": "index",
          "style": {
            "navigationBarTitleText": "分销管理",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "apply/index",
          "style": {
            "navigationBarTitleText": "申请成为分销员",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "distributionPoster/index",
          "style": {
            "navigationBarTitleText": "分销海报",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "distributionPoster/detail",
          "style": {
            "navigationBarTitleText": "海报详情",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "myCustomers/index",
          "style": {
            "navigationBarTitleText": "我的客户",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "myCustomers/detail",
          "style": {
            "navigationBarTitleText": "客户详情",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "withdrawalRecord/index",
          "style": {
            "navigationBarTitleText": "提现记录",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "withdrawalRecord/detail",
          "style": {
            "navigationBarTitleText": "提现详情",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
          "path": "distributionWithdrawal/index",
          "style": {
            "navigationBarTitleText": "分销提现",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        },
        {
            "path": "distributStatement/index",
            "style": {
                "navigationBarTitleText": "分账流水",
                "navigationStyle": "custom",
                "app-plus": {
                    "titleNView": {
                        "backgroundColor": "#fff"
                    }
                }
            }
        },
        {
          "path": "distributStatement/detail",
          "style": {
            "navigationBarTitleText": "分账详情",
            "navigationStyle": "custom",
            "app-plus": {
                "titleNView": {
                    "backgroundColor": "#fff"
                }
            }
          }
        },
        {
            "path": "log/index",
            "style": {
              "navigationBarTitleText": "分销记录",
              "navigationStyle": "custom",
              "app-plus": {
                "titleNView": {
                  "backgroundColor": "#fff"
                }
              }
            }
        },
        {
          "path": "client/bind-client/poster",
          "style": {
            "navigationBarTitleText": "关联客户",
            "navigationStyle": "custom",
            "app-plus": {
              "titleNView": {
                "backgroundColor": "#fff"
              }
            }
          }
        }
      ]
    }
],
    "tabBar": {
      "color": "#B8BABC",
      "selectedColor": "#B8BABC",
      "backgroundColor": "#ffffff",
      "list": [
        {
          "pagePath": "pages/accompany-home/index",
          "text": "首页",
          "iconPath": "static/image/business/icon-home.png",
          "selectedIconPath": "static/image/business/icon-home.png"
        },
        {
          "pagePath": "pages/service/index",
          "text": "服务",
          "iconPath": "static/image/business/icon-service.png",
          "selectedIconPath": "static/image/business/icon-service.png"
        },
        {
          "pagePath": "pages/order/index",
          "text": "订单",
          "iconPath": "static/image/business/icon-order.png",
          "selectedIconPath": "static/image/business/icon-order.png"
        },
        {
          "pagePath": "pages/my/index",
          "text": "我的",
          "iconPath": "static/image/business/icon-my.png",
          "selectedIconPath": "static/image/business/icon-my.png"
        }
      ],
      "borderStyle": "white"
    },
    "globalStyle": {
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#FFFFFF",
        "backgroundColor": "#F8F8F8",
        "backgroundColorTop": "#fff",
        "backgroundColorBottom": "#F4F5F6",
        "mp-alipay": {
          "allowsBounceVertical": "NO",
          "titleBarColor": "#FFFFFF"
        }
        // #ifdef MP-ALIPAY
        ,"usingComponents": {
          "ad": "/mycomponents/ad/index"
        }
        // #endif
    },
    "style": {
        "app-plus": {
            "animationType": "slide-in-right",
            "animationDuration": 300
        }
    },
    "condition": {
        //模式配置，仅开发期间生效
        "current": 0,
        //当前激活的模式(list 的索引项)
        "list": [{
            "name": "",
            //模式名称
            "path": "",
            //启动页面，必选
            "query": ""
                //启动参数，在页面的onLoad函数里面得到
        }]
    }
}
