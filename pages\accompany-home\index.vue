<template>
  <view class="page" :class="[skinColor ? 'index-' + skinColor : '',{'extend-bg': shouldExtendBg && showClassifySection}]" :style="{'--file-ctx': file_ctx + ''}">
    <view class="accompany-header" :class="{ bgHeader: !(shouldExtendBg && showClassifySection) }">
        <!-- <view class="bgHeader" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-service.png)','background-size': '100%'}"></view> -->
        <view :style="'height:' + statusBarHeight + 'px; width: 100%;'" class="accompany-content-son-1"></view>
        <view class="top-nav accompany-content-son-2">
          <view class="top-nav-c">{{serverOptionsTitle}}</view>
        </view>
        <view class="search-container" :class="{'blue-search-fixed': skinColor === 'blueSkin' && !showClassifySection, 'blue-search-fixed-2': skinColor === 'blueSkin' && showClassifySection}">
          <search style="padding: 0 !important;" class="accompany-content-son-3" :fixed="true" placeholder="搜索帖子、服务、医院" v-model="search" @handleCityFn="$refs.selectCity.show()">
            <template #cityName>{{cityName}}</template>
          </search>
        </view>
      </view>
    <!-- 搜索框 -->
    <view class="accompany-content">
      <scroll-view
        class="content-scroll"
        scroll-y="true"
        @scroll="handleContentScroll"
        :style="{ height: 'calc(100vh - 288rpx)' }"
      >
        <view class="scroll-content">
          <!-- 分类模块 -->
          <category-module
            class="accompany-content-son-1"
            :showClassifySection="showClassifySection"
            :categoryList="categoryList"
            :file_ctx="file_ctx"
            :skinColor="skinColor"
            @order-by-category="gotoOrderByCategory"
            @service-with-category="gotoServiceWithCategory"
          />

          <!-- banner部分 -->
          <view class="banner accompany-content-son-4">
            <swiper class="swiper" circular :indicator-dots="true" :autoplay="true" :interval="5000"
              :duration="500" indicator-active-color="#00B484">
              <swiper-item class="swiper-item" v-for="item in carouselList" :key="item.id" @tap="navigateTo(item)">
                <view class="item"><image class="img" :src="item.image"></image></view>
              </swiper-item>
            </swiper>
          </view>

          <!-- 专业陪诊部分 -->
          <view v-if="showServiceSection" class="specialty-accompany accompany-content-son-5" :style="{backgroundImage:'url(' + file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty.png' + ')','background-repeat':'no-repeat','background-size': '100%'}">
            <view class="accompany-head">
              <view class="accompany-head-l">
                <view class="teacher" :style="{'background-image':'url('+ file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty-teacher.png)','background-size': '100%'}"></view>
                <view class="info">
                  <view class="info-t" :style="{'background-image':'url('+ file_ctx + 'static/image/business/accompany-doctor/icon-accompany-doctor-specialty-text.png)','background-size': '100%'}"></view>
                  <view class="info-b">1000+陪诊师</view>
                </view>
              </view>
              <view class="accompany-head-r" @tap="gotoOrder(accompanyMap[0])">
                <button>立即预约</button>
              </view>
            </view>
            <view class="accompany-bott">
              <serverShow :serverMap="accompanyMap" :skinColor="skinColor"></serverShow>
            </view>
          </view>

          <!-- 模块盒子 -->
          <moduleBox @changePage="(pageNum)=>$emit('changePage',pageNum)" class="accompany-content-son-6" :skinColor="skinColor" :accompanyproviderId="accompanyproviderId" :cityName='cityName' :serviceList='serviceList' :postList='postList' :indexlist='indexlist' :hospitalList='hospitalList' :accompanylist='accompanylist'></moduleBox>
        </view>
      </scroll-view>
    </view>

    <!-- 城市套餐 -->
    <view class="lineHide">
      <dataPicker ref="selectCity" :localdata="accompanyproviderList" popup-title="请选择就诊城市" @change="onchangeCity"></dataPicker>
    </view>
      <!-- 加载动画 -->
    <view v-if="!animationFlag" class="loading-container" :class="{ 'is-loading': !animationFlag}">
      <view class="isMask" :class="{ 'Mask': showMask}"></view>
      <view class="dual-ring"></view>
    </view>
    <customerService ref="customerService"></customerService>
    <rapidConsultation ref="rapidConsultation" :pageIndex='pageIndex' :popupTitle='(pageIndex === 3 || pageIndex === 4) ? "联系客服，取消订单" : "" ' v-if="showRapidConsultation"></rapidConsultation>
  </view>
</template>

<script>
  import dataPicker from './components/uni-data-picker/uni-data-picker.vue'
  import serverOptions from '@/config/env/options'
  import { mapState } from 'vuex'
  import search from './components/search'
  import uniPopup from '@/components/uni/uni-popup'
  import { isDomainUrl } from '@/utils/index.js'
  import moduleBox from './components/moduleBox.vue'
  import { getQueryObject } from '@/utils/index'
  import getPosition from '@/utils/getPosition'
  import customerService from './components/customerService.vue'
  import rapidConsultation from './components/rapidConsultation.vue'
  import categoryModule from './components/categoryModule.vue'
  import serverShow from './components/serverShow.vue'

  export default {
    components: {
      // uniNavBar,
      search,
      // UniIcons,
      uniPopup,
      dataPicker,
      moduleBox,
      serverShow,
      customerService,
      rapidConsultation,
      categoryModule
    },
    data(){
      return {
        pageIndex: 1,
        animationFlag:false,
        showRapidConsultation: false,
        showClassifySection: false,
        showServiceSection: true,
        file_ctx: this.file_ctx,
        statusBarHeight: 0,
        navList:[
          {name:'首页',url:'static/image/business/accompany-doctor/icon-home.png',activeUrl:'static/image/business/accompany-doctor/icon-home-active.png'},
          {name:'服务',url:'static/image/business/accompany-doctor/icon-accompany-bottom-service.png',activeUrl:'static/image/business/accompany-doctor/icon-accompany-bottom-service-active.png'},
          {name:'订单',routerName:'accompanyOrder',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'},
          {name:'我的',url:'static/image/business/accompany-doctor/icon-my.png',activeUrl:'static/image/business/accompany-doctor/icon-my-active.png'}
        ],
        search:'',
        cityName:'...',
        serviceList:[],
        postList:[],
        indexlist:[],
        accompanylist:[],
        hospitalList:[],
        navCurrent:0,
        accompanyMap:[],
        accompanyproviderList:[],
        accompanyproviderId:serverOptions.providerId,
        carouselList:[],
        serviceCurrent:0,
        categoryList: [],
        categoryPages: 1,
        showClassifySection: false,
        showMask:true,
        // 触摸滑动所需变量
        startY: 0,
        endY: 0,
        distance: 0,
        isScrollingDown: false,
        // 背景控制相关变量
        scrollTop: 0,
        isBlueAndScrolled: false,
        shouldExtendBg: true, // 默认为true，确保进入页面就扩展背景
        categoryModuleHeight: 100,  // 设置默认值
      }
    },
    mounted() {

    },
    onShow() {
      console.log('首页显示');
      // 发送自定义事件通知组件页面已显示
      uni.$emit("accompanyHome:show");

      // 通知moduleBox组件页面已显示
      if (this.$refs.moduleBox) {
        this.$refs.moduleBox.onShow && this.$refs.moduleBox.onShow();
      }
    },
    onHide() {
      console.log('首页隐藏');
      // 发送自定义事件通知组件页面已隐藏
      uni.$emit('accompanyHome:hide');

      // 通知moduleBox组件页面已隐藏
      if (this.$refs.moduleBox) {
        this.$refs.moduleBox.onHide && this.$refs.moduleBox.onHide();
      }
    },
    onUnload() {
      console.log('首页卸载');
      // 页面卸载时发送隐藏事件
      uni.$emit('accompanyHome:hide');
    },
    // 通过watch监听skinColor变化
    watch: {
      skinColor: {
        immediate: true,  // 组件创建时立即执行一次
        handler(newVal) {
          // 皮肤变化时重新检查是否需要扩展背景
          if (this.showClassifySection) {
            this.shouldExtendBg = true;
          } else {
            this.shouldExtendBg = false;
          }
        }
      },
      showClassifySection: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            this.shouldExtendBg = true;
          }
        }
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId
      }),
      serverOptionsTitle(){
        return serverOptions.title;
      }
    },
    created() {
      if (this.showClassifySection) {
        this.shouldExtendBg = true;
      } else {
        this.shouldExtendBg = false;
      }
    },

    async onLoad(res){
      if (this.showClassifySection) {
        this.shouldExtendBg = true;
      } else {
        this.shouldExtendBg = false;
      }
      let params = decodeURIComponent(decodeURIComponent(decodeURIComponent(res.scene)))
      let sceneObj = getQueryObject(params)
      console.log('sceneObj',sceneObj)
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);

      // 获取服务商信息，判断是否显示急速问诊组件
      try {
        const { data } = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id: serverOptions.providerId});
        // 判断联系客服开关是否开启，且位置包含首页
        if (data.contactButton === 1 && data.contactPosition && data.contactPosition.includes('1')) {
          this.showRapidConsultation = true;
        }
        console.log(data.classifyButton,data.serviceButton, 'data.classifyButton,data.serviceButton');

        // 控制分类模块和专业陪诊部分的显示与隐藏
        this.showClassifySection = data.classifyButton === 1; // 分类模块默认关闭，只有设置为1时才显示
        this.showServiceSection = data.serviceButton !== 0; // 专业陪诊服务默认开启，只有设置为0时才隐藏
      } catch (error) {
        console.error('获取服务商信息失败:', error);
      }

      // 处理扫码进入的参数
      if (sceneObj.id) {
        try {
          // 调用绑定分销员接口
          await this.$api.distribution.accompanydistributorcustomerInsert({
            distributorId: sceneObj.id,  // 分销员id
            userId: codeUserInfo.id
          });
        } catch (error) {
          console.error('绑定分销员失败:', error);
          uni.showToast({
            title: '绑定失败',
            icon: 'none',
            duration: 2000
          });
        }
      }
      console.log('页面开始加载', + new Date());
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
      let loadServer = this.loadServer()
      let accompanyserviceQueryCity = this.accompanyserviceQueryCityPage();
      let accompanycomboQueryCity = this.accompanycomboQueryCityPage();
      let bannerQuery = this.bannerQueryPage();
      // 获取分类列表
      let categoryQuery = this.getCategoryList();
      await Promise.all([loadServer,accompanyserviceQueryCity,accompanycomboQueryCity,bannerQuery,categoryQuery])
      this.animationFlag = true;
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        // 获取分类模块的高度
        query.select('.accompany-content-son-1').boundingClientRect(data => {
          if (data) {
            this.categoryModuleHeight = data.height || 100;
          }
        }).exec();
      }, 500); // 增加延时确保DOM已渲染
    },
    onShareAppMessage(){
      let shareOptions = this.mixinsShareOptions()
      return {
        title:serverOptions.title,
        ...shareOptions
      }
    },
    onShareTimeline(){
      return {
        title: serverOptions.title,
        path: '/pages/accompany-home/index'
      }
    },
    methods:{
      // 处理内容区域滚动事件
      handleContentScroll(e) {
        // 获取滚动位置
        this.scrollTop = e.detail.scrollTop;

        // 判断是否需要扩展背景（适用于所有皮肤）
        if (this.showClassifySection) {
          // 使用固定值：当滚动超过100px时取消背景扩展
          const SCROLL_THRESHOLD = 100; // 固定扩展高度，滚动超过100px就恢复原背景
          this.shouldExtendBg = this.scrollTop < SCROLL_THRESHOLD;
          this.isBlueAndScrolled = this.scrollTop > SCROLL_THRESHOLD;
        }
      },
      handletapJump(index){
        this.navCurrent = index
      },
      gotoOrder(options){
        if (!options) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
          return;
        }
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$navto.push('ServiceDetail',{id:options?.id, classifyId: options?.classifyId || '', name: options?.classifyName || '',city});
      },
      gotoOrderJump(options){
        if (!options) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
          return;
        }
        this.$navto.push('serviceReservation',{id:options?.id, classifyId: options?.classifyId || '', name: options?.classifyName || ''});
      },

      // 分类模块立即预约按钮点击事件
      gotoOrderByCategory(category){
        if (!category) {
          uni.showToast({
            title: '暂无分类数据',
            icon: 'none'
          });
          return;
        }
        // 传递分类ID和名称到服务预约页面
        this.$navto.push('serviceReservation', {classifyId: category.classifyId, name: category.name});
      },

      gotoServiceWithCategory(item) {
        if (!item) {
          uni.showToast({
            title: '暂无数据',
            icon: 'none'
          });
          return;
        }
        // 点击分类项，传递分类ID和名称到服务预约页面
        this.$navto.push('serviceReservation', {classifyId: item.classifyId, name: item.name});
      },
      // 服务分类列表获取
      getCategoryList() {
        this.$api.accompanyDoctor.accompanyserviceclassifyQueryPage({
          current: 1,
          size: 16,
          condition: {
            state: 1,
            providerId: this.accompanyproviderId
          }
        }).then(res => {
          if (res.data && res.data.records) {
            // 按照orderValue排序，值小的在前面
            const sortedRecords = [...res.data.records].sort((a, b) => {
              // 如果orderValue不存在，默认为0
              const orderA = a.orderValue != null ? a.orderValue : 0;
              const orderB = b.orderValue != null ? b.orderValue : 0;
              return orderA - orderB;
            });

            this.categoryList = sortedRecords.map(item => ({
              classifyId: item.id,
              name: item.name,
              icon: item.classifyIcon || '/static/image/business/accompany-doctor/indexIcon%20(4).png' // 添加默认图标
            }));
            // 计算分类页数
            this.categoryPages = Math.ceil(this.categoryList.length / 4);
          }
        }).catch(err => {
          console.error('获取服务分类失败:', err);
        });
      },
      // 获取指定页的分类项 - 已迁移到组件中
      // getCategoryPageItems(pageIndex) {
      //   const start = pageIndex * 4;
      //   const end = start + 4;
      //   return this.categoryList.slice(start, end);
      // },
      // 服务列表
      async accompanyserviceQueryCityPage(){
        let {data:{records}} = await this.$api.accompanyDoctor.accompanyserviceQueryCityPage({current:1,size:32,condition:{state:1,city:this.cityName}})
        this.accompanyMap = records
        console.log('accompanyMap',records);

      },
      // 套餐列表
      accompanycomboQueryCityPage(){
        let resFn;
        let pro = new Promise(res=>resFn = res)
        const city = this.$common.getKeyVal('user','cityName',true)
        this.$api.accompanyDoctor.accompanycomboQueryCityPage({current:1,size:2,condition:{state:1,city}}).then(res=>{
          this.serviceList = res.data.records.map(item=>({...item,cityPrice:item.cityPrice ? item.cityPrice / 100 : ''}))
          resFn()
        })
        return pro
      },

      // 首页弹窗
      bannerQueryPage(){
        let resFn;
        let pro = new Promise(res=>resFn = res)
        let params = {
          current:1,
          size:10,
          condition:{
            openStatus: 1,
            useType: 8,
            useTypes: [1, 5, 6,8],
            providerId:this.accompanyproviderId
          }
        }
        this.$api.drugBook.bannerQueryPage(params).then(res=>{
          this.carouselList = res.data.records.map(item=>({...item,image:isDomainUrl(item.image)}))
          resFn()
        })
        return pro
      },
      onchangeCity({detail:{value}}){
        this.cityName = value[1].text
        let pname = value[0].text
        this.$common.setKeyVal('user','pname',pname,true)
        this.$common.setKeyVal('user','cityName',this.cityName,true)
        this.postmessageQueryRecommendPage(this.accompanyproviderId)
        this.hospitalQueryPage()
        this.hospitaldoctorQueryPage();
        this.accompanyemployeeQueryPage();
        this.accompanycomboQueryCityPage()
      },
      async loadServer(){
        let cityname = this.$common.getKeyVal('user','cityName',true)
        let pname = this.$common.getKeyVal('user','pname',true)
        let cityRes;
        console.log('调用请求授权', + new Date());
        cityRes = (cityname && pname) ? {cityname,pname} : await getPosition.initLocationPerm()
        if(!cityRes) return
        // 判断当前是否是平台
        let queryOptions;
        if(serverOptions.source === 1){
          queryOptions = (await this.$api.accompanyDoctor.getAccompanyproviderAll()).data
        }else{
          queryOptions = [(await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data]
        }
        let cityMap = this.getCityMap(queryOptions)
        this.accompanyproviderList = [...new Set(cityMap)].filter(e=>e);
        // 判断当前用户所处的城市 是否存在于当前服务商所选择的城市列表中
        let cityList = this.accompanyproviderList.map(e=>e.children).flat()
        let cityIndex = cityList.findIndex(e=>e.text === cityRes.cityname)
        this.cityName = cityRes.cityname;
        console.log('cityMap',JSON.parse(JSON.stringify(cityMap)),cityRes,cityIndex);
        if(cityIndex < 0){
          this.cityName = this.accompanyproviderList[0].children[0].text
          uni.showToast({
            title:`您当前城市暂未提供陪诊服务，已为您切换【${this.cityName}】城市`,
            icon:'none',
            duration: 4000
          })
        }
        cityname || this.$common.setKeyVal('user','cityName',this.cityName,true)
        pname || this.$common.setKeyVal('user','pname',cityRes.pname,true)
        let postmessage = this.postmessageQueryRecommendPage(this.accompanyproviderId)
        let hospital = this.hospitalQueryPage()
        let hospitaldoctor = this.hospitaldoctorQueryPage();
        let accompanyemployee = this.accompanyemployeeQueryPage();
        await Promise.all([postmessage,hospital,hospitaldoctor,accompanyemployee])
      },
      getCityMap(AccompanyproviderAll){
          return AccompanyproviderAll.reduce((acc, {province, city}) => {
              let provinceMap = province.split(',');
              let cityMap = city.split('$');
              provinceMap.map((provinceItem,index)=>{
              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap)
              })
              return acc;
          }, []).map(e=>{
              e.children = [...new Set(e.children)];
              e.children = e.children.map(e=>({text:e,value:e}))
              return e;
          });
      },
      // 推荐帖子列表
      postmessageQueryRecommendPage(id){
        let resFn;
        let pro = new Promise(res=>resFn = res)
        const param = {
          current: 1,
          size: 2,
          condition: {
            accountId: this.accountId,
            providerId: id,
            entryType: 5,
          }
        }
        this.$ext.community.postmessageQueryRecommendPage(param).then(res=>{
          this.postList = res.data.records.slice(0, 2);
          resFn()
        })
        return pro
      },

      // 医生列表
      hospitaldoctorQueryPage(){
        let params = {
          current: 1,
          size: 3,
          condition: {
            city: this.cityName?.split('市')[0] || '',
          }
        }
        let defaultUrl = '0/msg-reply/1013082175633223682.png';
        this.$api.hospital.crawlershospitaldoctorQueryPage(params).then(res => {
            let data = res.data.records.map(item=>({...item,expertPic:isDomainUrl(item.expertPic || defaultUrl)})) || []
            this.indexlist = data
        })
      },
      accompanyemployeeQueryPage(){
        const xiaohuluProviderId = '2124021789005144070'; // 小葫芦陪诊的 providerId
        const isXiaoHuLu = serverOptions.providerId === xiaohuluProviderId;

        let params = {
          current: 1,
          size: 100,
          condition: {
            city: this.cityName,
            language: null,
            auditStatus: 2
          }
        };

        if (isXiaoHuLu) {
          // 如果是小葫芦陪诊，添加 omitProviderId 标记
          params.omitProviderId = true;
        } else {
          // 其他服务商，正常设置 providerId
          params.condition.providerId = this.accompanyproviderId;
        }

        this.$api.accompanyDoctor.accompanyemployeeQueryStarPage(params).then(res => {
            let data = res.data.records.map(item=>({
                ...item,
                avatar:isDomainUrl(item.avatar),
                language: item.language ? item.language.split(',') : [] // 安全处理 language 可能为 null 或 undefined 的情况
              })) || []
              console.log('陪诊师数据总数:', data.length);
            this.accompanylist = data
        }).catch(err => {
            console.error("获取陪诊师列表失败:", err);
            this.accompanylist = []; // 发生错误时清空列表或进行其他错误处理
        });
      },

      // 医院列表
      hospitalQueryPage(){
        let resFn;
        let pro = new Promise(res=>resFn = res)
        let params = {
          current: 1,
          size: 2,
          condition: {
            city: this.cityName.replace(/市市辖区|市辖区|市/g,''),
          }
        }
        this.$api.hospital.hospitalQueryPage(params).then(res => {
          this.hospitalList = res.data.records.map(item=>({...item,logo:isDomainUrl(item.logo)})) || []
          resFn()
        })
        return pro
      },

    },
  }
</script>

<style lang="scss" scoped>
  @import '../style/blueSkin.scss';
.lineHide{
  width: 0;
  overflow: hidden;
  height: 0;
}
.accompany-header{
  position: sticky;
  top: 0px;
  z-index: 99;
  padding: 0 32rpx;
  box-sizing: border-box;
}
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(255, 255, 255, 0);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease-in-out;
  z-index: -1;
}
.isMask{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: white;
  opacity: 0;
}
.Mask{
  opacity: 1;
}
/* 默认背景样式 */
.bgHeader {
  background-image: url(https://file.greenboniot.cn/static/image/business/accompany-doctor/icon-accompany-service.png) !important;
  background-size: 100% !important;
  background-repeat: no-repeat !important;
}
.loading-container.is-loading {
  opacity: 1;
  visibility: visible;
  z-index: 9999;
}
.search-container {
  position: relative; /* 默认相对定位 */
  z-index: 10;
}
/* 只有蓝色皮肤且没有分类时应用固定定位 */
.blue-search-fixed {
  position: absolute;
  bottom: 10rpx;
  left: 0;
  right: 0;
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}
.dual-ring {
  display: inline-block;
  width: 64px;
  height: 64px;
  transform: scale(0.8);
  transition: transform 0.3s ease-in-out;
}

.is-loading .dual-ring {
  transform: scale(1);
}

.dual-ring:after {
  content: " ";
  display: block;
  width: 46px;
  height: 46px;
  margin: 8px;
  border-radius: 50%;
  border: 6px solid #3498db;
  border-color: #3498db transparent #3498db transparent;
  animation: dual-ring 1.2s linear infinite;
}

@keyframes dual-ring {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
  .img{
    width: 100%;
    height: 100%;
  }
  .page{
    // position: fixed;
    width: 100vw;
    height: 100vh;
    background: #F4F6FA;

    // padding: 20rpx 0;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    .bgHeader{
      height: 288rpx;
      width: 100%;
    }
  }
    /* 添加content-scroll样式 */
  .content-scroll {
    width: 100%;
    box-sizing: border-box;
  }

  /* scroll-content样式 */
  .scroll-content {
    display: flex;
    flex-direction: column;
  }
  .accompany-content{
    // padding-bottom: 166rpx;
    margin:0 32rpx;
    position: relative;
  }
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;

    /* 小屏幕适配 */
    @media screen and (max-height: 667px) {
      height: 60px;
      line-height: 60px;
    }
    // padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .banner{
    width: 686rpx;
    height: 208rpx;
    // background: #DAE6FF;
    border-radius: 16rpx;
    margin: 32rpx 0;
    .swiper{
      height: 100%;
      border-radius: 16rpx;
      overflow: hidden;
      .swiper-item{
        width: 100%;
        height: 100%;
        .item{
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .specialty-accompany{
    position: relative;
    width: 686rpx;
    // height: 312rpx;
    padding: 0 24rpx 24rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 16rpx;
    .accompany-head{
      display: flex;
      height: 150rpx;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #e5e5e5;
      margin-bottom: 24rpx;
      .accompany-head-l,.accompany-head-r{
        display: flex;
        align-items: center;
        .teacher{
          position: absolute;
          top: -10rpx;
          width: 114rpx;
          height: 160rpx;
        }
        .info{
          display: flex;
          flex-direction: column;
          margin-left: 128rpx;
          .info-t{
            width: 144rpx;
            height: 36rpx;
            margin-bottom: 10rpx;
          }
          .info-b{
            font-size: 28rpx;
            color: #1D2029;
            line-height: 40rpx;
          }
        }
      }
      .accompany-head-r{
        button{
          display: flex;
          justify-content: center;
          align-items: center;
          width: 160rpx;
          height: 72rpx;
          background: #00B484;
          box-shadow: 0rpx 0rpx 8rpx 0rpx #FFFFFF;
          border-radius: 36rpx;
          font-weight: 600;
          font-size: 24rpx;
          color: #FFFFFF;
          line-height: 34rpx;
          &::after{
            border: none !important;
          }
        }
      }
    }
    .accompany-bott{
      display: flex;
      flex-wrap: wrap;
      height: 310rpx;
      position: relative;
      .accompany-bott-item{
        height: 136rpx;
        width: 310rpx;
        margin-right: 18rpx;
        margin-bottom: 18rpx;
        padding: 24rpx;
        box-sizing: border-box;
        .title{
          font-size: 30rpx;
          color: #1D2029;
          line-height: 42rpx;
          font-weight: 600;
          white-space: nowrap;      /* 防止文本换行 */
          overflow: hidden;         /* 超出部分隐藏 */
          text-overflow: ellipsis;  /* 显示省略号 */
        }
        .text{
          width: 207rpx;
          margin-top: 2rpx;
          font-size: 22rpx;
          color: #A5AAB8;
          line-height: 32rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        &:nth-child(2n){
          margin-right: 0;
        }
      }
      .no-data {
        width: 100%;
        text-align: center;
        font-size: 28rpx;
        color: #A5AAB8;
        padding: 30rpx 0;
      }
    }
  }
  .accompany-bottom{
    position: fixed;
    width: 100%;
    bottom: 0;
    left:0;
    display: flex;
    z-index: 999;
    // align-items: center;
    justify-content: space-around;
    // height: 98rpx;
    height: 166rpx;
    background-color: #fff;
    .bottom-item{
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      padding-top: 8rpx;
      .bottom-item-img{
        width: 56rpx;
        height: 56rpx;
        margin-bottom: 4rpx;
      }
      .bottom-item-name{
        font-size: 20rpx;
        color: #868C9C;
      }
      .active{
        color: #00B484;
      }
    }
  }
  .guard-detail-content{
    position: relative;
    background: #fff;
    padding: 32rpx 32rpx 386rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .title{
      display: flex;
      justify-content: center;
      font-size: 32rpx;
      color: #1D2029;
      line-height: 44rpx;
    }
    .error{
      position: absolute;
      right: 32rpx;
      top: 38rpx;
      width: 32rpx;
      height: 32rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .city-tilte{
      font-size: 28rpx;
      color: #1D2029;
      line-height: 40rpx;
      margin: 40rpx 0 24rpx;
    }
    .city-list{
      display: flex;
      flex-wrap: wrap;
      .city-item{
        .city-item-content{
          width: 100%;
          max-height: 3em;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap; /* 防止文本换行 */
          overflow: hidden;    /* 隐藏溢出的内容 */
          text-overflow: ellipsis; /* 在溢出的地方显示省略号 */
          text-align: center;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%,-50%);
        }
        position: relative;
        width: 156rpx;
        height: 72rpx;
        background: #F4F6FA;
        border-radius: 12rpx;
        font-size: 26rpx;
        color: #1D2029;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        &:nth-child(4n){
          margin-right: 0;
        }
      }
      .currentItem{
        background: #D7FAF1;
        border: 1rpx solid #00B484;
        color: #00B484;
      }
    }
    .info{
      font-size: 24rpx;
      color: #868C9C;
      line-height: 34rpx;
      margin-top: 12rpx;
    }
  }



  .serverShow-blueSkin{
    height: 100%;
    .swiper-item{
      .item{
        padding-left: 54rpx;
        padding-top: 36rpx;
        display: flex;
        flex-wrap: wrap;

        .accompany-bott-item{
          background-color: rgba(255, 255, 255, 0);
          width: 112rpx;
          margin: 0 0 20rpx 0;
          margin-right: 44rpx;
          height: 138rpx;
          padding: 0;
          text-align: center;

          .icon{
            display: inline-block;
            width: 90rpx;
            height: 90rpx;
          }

          .title{
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            text-align: center;
          }
        }
      }
    }
  }

  .swiper-dot{
    position: absolute;
    bottom: 12rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;

    .swiper-dot-item{
      width: 12rpx;
      height: 12rpx;
      background: rgba(51,51,51,0.1);
      border-radius: 50%;
      margin-right: 12rpx;

      &.active{
        background: #00B484;
      }
    }
  }
  ::v-deep .search {
    padding: 0 !important;
  }
</style>
