<template>
  <view class="">
    <view class="boxTab" :class="{noScroll:isFocus}">
      <!-- 头部服务选择· -->
      <view class="headerTab">
        <image class="serverIcon" :src="file_ctx + currentServer.listImg" mode=""></image>
        <!-- 服务名称 -->
        <view class="serviceName">
          <view class="">
            <view v-if="classifyName" class="classify-name">{{classifyName}}</view>
            {{currentServer.serviceName}}
          </view>
          <view class="">
            <text class="signal">¥</text>
            <span class="serverNum">
              <span v-if="currentServer.cityPrice">{{currentServer.cityPrice / 100}}</span>
              <span v-else>{{currentServer.price / 100}}</span>
            </span>
            <text class="tag">/次起</text>
          </view>
        </view>
        <!-- 更换服务 -->
        <view class="changeServer" @click="handleChangeServer">更换服务</view>
        <!-- 挂号提示 -->
        <view class="registrationPrompts">
          <image class="warningGreen" :src="warningGreen" mode=""></image>
          如需协助挂号，可联系在线客服
        </view>
      </view>
      <view class="botTab">
        <view class="botTabLine" @click="handleSelectCity" >
          <view class="lineTitle"><span class="required">*</span>就诊城市</view>
          <view class="lineValue">{{getSelectCity}}</view>
          <image class="lineIcon" :src="iconRightArrow" mode=""></image>
        </view>
        <view class="lineHide">
          <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择就诊城市" @change="onchangeCity" @nodeclick="onnodeclick"></dataPicker>
        </view>
        <view class="botTabLine" @click="handleSelectHospital">
          <view class="lineTitle">就诊医院</view>
          <view class="lineValue">{{hospitalName}}</view>
          <image class="lineIcon" :src="iconRightArrow" mode=""></image>
        </view>
        <view class="lineHide">
          <selectData ref="selectHospital" v-model="queryOptions.hospitalName" placeholder="输入医院名称，模糊匹配搜索"  :localdata="hospitalQuery" popup-title="请选择就诊医院" @change="onchangeHospital" @nodeclick="onnodeclick"></selectData>
        </view>
        <view class="botTabLine" @click="handleSelectTime">
          <view class="lineTitle"><span class="required">*</span>就诊时间</view>
          <view class="lineValue">{{getSelectTime}}</view>
          <image class="lineIcon" :src="iconRightArrow" mode=""></image>
        </view>
        <view class="lineHide">
          <timePicker ref="selectTime" v-model="queryOptions.range" v-if="!timeType" type="datetime" @maskClick="maskClick" />
          <timePicker ref="selectTime" v-model="queryOptions.range" v-if="timeType" type="datetimerange" @maskClick="maskClick" />
        </view>
        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>就诊人</view>
          <view class="bookInput patient-select-input" @click="handlePatientInputFocus">
            <text class="patient-name-text">{{queryOptions.bookName || '请选择就诊人'}}</text>
            <image class="select-arrow" :src="$static_ctx + 'image/business/hulu-v2/icon-right-arrow.png'" mode="aspectFit"></image>
          </view>
        </view>
        <view class="botTabLine">
          <view class="lineTitle"><span class="required">*</span>就诊人电话</view>
          <input type="text" @focus="handleBookPhoneFocus" @blur="inputFocus" placeholder="请填写您的联系电话" class="bookInput" v-model="queryOptions.bookPhone"/>
        </view>
        <view class="additionalContent">
          <view class="lineTitle">补充内容</view>
          <view class="additionalContentImageUpMap">
            <view
            class="additionalContentImageUp"
            v-for="(item,index) in queryOptions.imageObj"
            :key="index"
            @click="handlePreviewImage(index)"
            >
              <image class="iconPostMenuClose" @click.stop="handleDeleteImage(index)" :src="iconPostMenuClose" mode=""></image>
              <image class="imageContent" :src="item.url"></image>
            </view>
            <view class="additionalContentImageUp" @click="handleUploadImage">
              <image class="imageUpImage" :src="iconPostUpload" mode=""></image>
              <view class="imageUpText">上传图片</view>
            </view>
          </view>
        </view>
        <view class="botTabLine textarea">
          <view class="lineTitle">备注</view>
          <textarea @focus="handleRemarkFocus" @blur="inputFocus" placeholder="请填写您的备注" class="bookTextarea" v-model="queryOptions.remark"></textarea>
        </view>
        <view class="lineHide">
          <title-img :config="{}" ref="upDataImage" @returnFn="imgReturnFn" :cData="queryOptions.imageObj"></title-img>
        </view>
      </view>
      <!-- 底部 -->
      <view class="bottomBtn">
        <!-- 同意协议 -->
        <agreement ref="agreementRef" v-model="isAgreed" :provinceValue="provinceValue" :fromQRCode="fromQRCode"></agreement>
        <view class="comBtn" :class="{disabled: isSubmitting}" @click="reservation">{{ isSubmitting ? '提交中...' : '立即预约' }}</view>
      </view>

    </view>

    <!-- 自定义弹窗 -->

    <!-- 就诊人选择弹窗 -->
    <patientSelectModal :provinceValue="provinceValue" @selectPatient="selectPatient" ref="patientSelectModal"></patientSelectModal>
  </view>
</template>

<script>
  import timePicker from '../components/uni-datetime-picker/uni-datetime-picker'
  import dataPicker from '../components/uni-data-picker/uni-data-picker.vue'
  import selectData from '../components/select-data.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import patientSelectModal from '../components/patientSelectModal.vue'
  import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'
  import { mapState } from 'vuex' // 导入 mapState
  import agreement from "../components/agreement.vue";

  export default{
    components: {
      timePicker,
      dataPicker,
      TitleImg,
      selectData,
      patientSelectModal,
      agreement
    },
    props:{
      currentServer:{
        type:Object,
      },
      cityRes:{
        type:Object
      },
      channelCode:{
        type:String
      },
      classifyId: {
        type: String,
        default: ''
      },
      classifyName: {
        type: String,
        default: ''
      },
      fromQRCode: {
        type: Boolean,
        default: false
      }
    },
    data(){
      return {
        isFocus:false,
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        iconPostUpload: this.$static_ctx + "image/business/hulu-v2/icon-post-upload.png",
        iconPostMenuClose: this.$static_ctx + "image/business/hulu-v2/icon-post-menu-close.png",
        warningGreen: this.$static_ctx + "image/business/hulu-v2/warning-green.png",
        file_ctx: this.file_ctx,
        provinceMap:[],
        hospitalQuery:[],
        hospitalName:'',
        queryOptions:{
          provinceValue:[],
          hospitalName:'',
          imageObj:[],
          range:[],
          bookName:'',
          bookPhone:'',
          remark:''
        },
        isAgreed: true, // 是否同意协议
        provinceValue:null,
        showCustomModal: false, // 控制自定义弹窗的显示与隐藏
        showPatientSelectModal: false, // 控制患者选择弹窗的显示与隐藏
        selectedPatient: null,
        tempPatient: {
          name: '',
          sex: 1,
          symptom: '行动自如'
        },
        isSubmitting: false, // 防止重复提交标志
        orderId: '' // 存储订单ID，用于判断是否是支付页
      }
    },
    computed:{
      ...mapState('user', { //映射 isLogin 状态
        isLogin: state => state.isLogin
      }),
      getSelectCity(){
        return this.queryOptions.provinceValue.join()
      },
      getSelectTime(){
        console.log('this.queryOptions.range',this.queryOptions.range);
        if(Array.isArray(this.queryOptions.range) && this.queryOptions.range.length > 0){
          return this.queryOptions.range.join('至')
        }
        return this.queryOptions.range
      },
      timeType(){
        if(!this.provinceValue) return false
        if(this.provinceValue.manualButton === 2) return true
        return this.provinceValue.manualButton === 0
      }
    },
    watch: {
      cityRes: {
        handler(val) {
          if(this.cityRes?.cityname){
            this.getHospital({province:this.cityRes.pname,city:this.cityRes.cityname})
            this.queryOptions.provinceValue = [this.cityRes.pname,this.cityRes.cityname]
            console.log('this.queryOptions.provinceValue',this.queryOptions.provinceValue);
          }
        },
        deep: true
      },
    },
    onPageScroll(){
      console.log('正在滚动');

    },
    async mounted() {
      let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
      this.provinceValue = provinceValue;
      // 获取城市列表
      this.provinceMap = await this.accompanyproviderQueryPage(provinceValue);
      // 监听选择患者事件
      uni.$on('selectPatient', this.handleSelectPatient);

      // 如果有分类ID
      if (this.classifyId) {
        await this.getServicesByClassifyId();
      }
    },
    onUnload() {
      // 移除事件监听，避免内存泄漏
      uni.$off('selectPatient', this.handleSelectPatient);
    },
    methods:{
      handleChangeServer(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$emit('changeServer');
      },
      handleSelectCity(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.selectCity.show();
      },
      handleSelectHospital(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.selectHospital.show();
      },
      handleSelectTime(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.selectTime.show();
      },
      handleBookPhoneFocus(event){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.inputFocus(event);
      },
      handlePreviewImage(index){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.upDataImage.previewImage(index);
      },
      handleDeleteImage(index){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.upDataImage.del(index);
      },
      handleUploadImage(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.upDataImage.uploadImage();
      },
      handleRemarkFocus(event){
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.inputFocus(event);
      },
      inputFocus({type}){
        this.isFocus = type === 'focus';
      },
      selectPatient({name,phone,sex,idcard,age}){
        this.queryOptions.bookName = name;
        this.queryOptions.sex = sex;
        this.queryOptions.idcard = idcard;
        this.queryOptions.bookPhone = phone || '';
        this.queryOptions.age = age;
      },
      async accompanyproviderQueryPage(provinceValue){
        // 判断当前是否是平台
        let queryOptions;
        if(serverOptions.source === 1){
          queryOptions = (await this.$api.accompanyDoctor.getAccompanyproviderAll()).data
        }else{
          queryOptions = [provinceValue]
        }
        let cityMap = this.getCityMap(queryOptions)
        return [...new Set(cityMap)].filter(e=>e);
      },
      getCityMap(AccompanyproviderAll){
          return AccompanyproviderAll.reduce((acc, {province, city}) => {
              let provinceMap = province.split(',');
              let cityMap = city.split('$');
              provinceMap.map((provinceItem,index)=>{
              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap)
              })
              return acc;
          }, []).map(e=>{
              e.children = [...new Set(e.children)];
              e.children = e.children.map(e=>({text:e,value:e}))
              return e;
          });
      },
      async getHospital({province,city}){
        let {data:{records:hospitalQuery}} = await this.$api.hospital.hospitalQueryPage({
          current:0,
          size:1000,
          condition:{
            province:province.replace(/省|市/g,''),
            // 去掉字符串里的市和市辖区还有市市辖区 使用正则祛除
            city:city.replace(/市市辖区|市辖区|市/g,''),
          }
          })
        hospitalQuery.map(e=>{
          e.text = e.hospitalName;
          e.value = e.hospitalName
        })
        console.log('hospitalQuery',hospitalQuery);
        this.hospitalQuery = hospitalQuery;
      },
      imgReturnFn(imageObj){
        console.log('imageObj',imageObj);
        this.queryOptions.imageObj = imageObj
      },
      changeServer(){
        this.$emit('changeServer');
      },
      maskClick(res){
        console.log('res',res);
      },

      onchangeCity({detail:{value}}){
        console.log('value',value);
        this.queryOptions.provinceValue = value.map(e=>e.value);
        console.log('this.queryOptions.provinceValue',this.queryOptions.provinceValue);
        let [province,city] = this.queryOptions.provinceValue;
        this.$emit('handleOneByCity',{province,city})
        this.getHospital({province,city})
      },
      onchangeHospital({detail:{value}}){
        console.log('value',value);
        this.hospitalName = value[0].text
      },
      onnodeclick(){},
      async reservation(){
        if (!this.isLogin) { this.gotoLogin(); return; }
        // 防止重复提交
        if (this.isSubmitting) {
          return;
        }
        // 检查是否选择了服务
        if (!this.currentServer.id) {
          uni.showToast({
            title: '请选择服务',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        // 先检查协议状态
        const isAgreed = await this.$refs.agreementRef.checkAgreement();
        if (!isAgreed) return;

        // 设置提交中状态
        this.isSubmitting = true;

        try {
          const accountId = common.getKeyVal('user', 'accountId', true);
          const codeUserInfo = common.getKeyVal('user', 'codeUserInfo',true)
          console.log('this.queryOptions',this.queryOptions);
          let options = {
            ...this.queryOptions,
            province:this.queryOptions.provinceValue[0],
            city:this.queryOptions.provinceValue[1],
            startTime:this.queryOptions.range,
            // endTime:this.queryOptions.range[1],
            backupImg:this.queryOptions.imageObj.map(e=>e.filePath).join(),
            serviceId:this.currentServer.id,
            classifyId: this.classifyId,
            accountId,
            userId:codeUserInfo.id,
            source:serverOptions.source,
            // channelCode:this.channelCode
          }
          // 处理受保人参数
          let dto = {
            name:this.queryOptions.bookName,
            phone:this.queryOptions.bookPhone,
            sex:this.queryOptions.sex,
            idcard:this.queryOptions.idcard,
            age:this.queryOptions.age,
          }
          options.dto = dto
          delete options.sex;
          delete options.idcard;
          if(Array.isArray(this.queryOptions.range)){
            options.startTime = this.queryOptions.range[0]
            options.endTime = this.queryOptions.range[1]
          }
          let requiredOptionsMap = ['province','city','startTime','bookName','bookPhone'];
          let returns = false;
          Object.keys(options).map(key=>{
            if(requiredOptionsMap.indexOf(key) >= 0 && !this.isEmpty(options[key])){
              returns = true;
              uni.showToast({title: '请填写信息',icon:'none'});
            }
          })
          if(returns) {
            this.isSubmitting = false; // 重置提交状态
            return;
          }
          if(this.provinceValue.manualButton === 2){
            let pro = new Promise((resolve,reject)=>{
              uni.showModal({
                title: '提示',
                content: '是否需要人工导诊服务？',
                confirmText:'需要',
                cancelText:'不需要',
                showCancel:true,
                complete: ({confirm})=> {
                  options.needManual = confirm
                  uni.showLoading({title:'提交中...'})
                  resolve(options)
                }
              });
            })
            await pro;
          }
          // 独立创单默认为独立陪诊模式
          options.store = 0;
          let {data} = await this.$api.accompanyDoctor.accompanybook(options);
          uni.hideLoading();

          // 更新订单ID
          this.orderId = data.id || '';
          console.log('订单提交成功，订单ID:', this.orderId);

          // 短暂延迟后刷新协议组件
          setTimeout(() => {
            if(this.$refs.agreementRef && this.orderId) {
              // 强制重新加载协议
              this.$refs.agreementRef.tempFilePathList = [];
              this.$refs.agreementRef.showAgreement = false;

              // 先设为null再重新赋值，触发watch
              const tempProvinceValue = {...this.provinceValue};
              this.$refs.agreementRef.provinceValue = null;

              setTimeout(() => {
                this.$refs.agreementRef.provinceValue = tempProvinceValue;
              }, 50);
            }
          }, 100);

          this.$emit('finish',data);
          !this.timeType && uni.showModal({
            title: '提示',
            content: '提交成功，客服将尽快与您联系',
            showCancel:false,
            success: (res)=> {

            }
          });
        } catch (error) {
          console.error('预约提交失败', error);
          uni.showToast({
            title: '提交失败，请重试',
            icon: 'none'
          });
        } finally {
          // 无论成功或失败，都重置提交状态
          this.isSubmitting = false;
        }
      },
      isEmpty(data){
        if(Array.isArray(data)){
          return data.length
        }
        return data
      },
      navtoGo(url = '', obj = {}) {
        this.$navto.push(url, obj)
      },
      // 处理从患者列表页选择的患者
      handleSelectPatient(patient) {
        console.log('接收到选择的患者', patient);
        if (patient && patient.name) {
          this.queryOptions.bookName = patient.name;
          this.queryOptions.bookPhone = patient.phone || '';
          this.queryOptions.sex = patient.sex;
          this.queryOptions.idcard = patient.idcard;
          this.queryOptions.age = patient.age;
          this.selectedPatient = patient;

          uni.showToast({
            title: '已自动填充患者信息',
            icon: 'none'
          });
        }
      },

      // 检查患者档案记录
      checkPatientRecord() {
        if (!this.queryOptions.bookName || this.queryOptions.bookName.length < 2) return;

        // 查找匹配姓名的患者档案
        const matchedPatient = this.patientRecords.find(record => record.name === this.queryOptions.bookName);

        if (matchedPatient) {
          // 如果找到匹配的患者档案，自动填充电话
          this.queryOptions.bookPhone = matchedPatient.phone || '';

        }
      },

      // 处理就诊人输入框点击
      handlePatientInputFocus() {
        if (!this.isLogin) { this.gotoLogin(); return; }
        this.$refs.patientSelectModal.showPatientSelect();
      },
      // 处理登录跳转，保存当前页面信息
      gotoLogin() {
        // 跳转到登录页面，并传递当前页面标识参数
        this.$navto.push('Login', {formPage: 'serviceReservation'});
      },
      // 快速添加就诊人
      async quickAddPatient() {
        if (!this.isLogin) { this.gotoLogin(); return; } // 添加登录检查
        if (!this.tempPatient.name) {
          uni.showToast({
            title: '请输入姓名',
            icon: 'none'
          });
          return;
        }

        try {
          uni.showLoading({
            title: '保存中...'
          });

          // 获取当前用户ID
          const codeUserInfo = common.getKeyVal('user', 'codeUserInfo', true);
          const accountId = common.getKeyVal('user', 'accountId', true);

          if (!codeUserInfo || !codeUserInfo.id) {
            uni.showToast({
              title: '用户信息获取失败',
              icon: 'none'
            });
            return;
          }

          // 准备患者数据
          const patientData = {
            name: this.tempPatient.name,
            sex: this.tempPatient.sex,
            symptom: this.tempPatient.symptom,
            userId: codeUserInfo.id,
            accountId: accountId,
            providerId: serverOptions.providerId
          };

          // 调用API保存患者信息
          const {data} = await this.$api.accompanyDoctor.accompanypatientInsert(patientData);

          // 添加成功后，填充到表单
          this.queryOptions.bookName = this.tempPatient.name;

          // 重置临时数据
          this.tempPatient.name = '';

          // 重新加载患者列表
          await this.loadPatientRecords();

          uni.hideLoading();
          uni.showToast({
            title: '添加成功',
            icon: 'success'
          });

          this.closePatientSelectModal();

        } catch (error) {
          uni.hideLoading();
          console.error('保存患者信息失败', error);
          uni.showToast({
            title: '保存失败，请重试',
            icon: 'none'
          });
        }
      },
       // 根据分类ID获取服务列表
       async getServicesByClassifyId() {
        try {
          // 如果当前服务已存在并且与分类匹配，不执行自动选择
          if (this.currentServer && this.currentServer.id) {
            // 检查当前服务是否已经包含正确的分类ID
            const hasCorrectClassify =
              (this.currentServer.classifyId && this.currentServer.classifyId === this.classifyId) ||
              (this.currentServer.categoryId && this.currentServer.categoryId === this.classifyId);

            if (hasCorrectClassify) {
              console.log('当前服务已与分类匹配，不执行自动选择', this.currentServer);
              return;
            }
          }

          // 如果URL中传递了服务ID，不执行自动选择第一个服务的逻辑
          const query = this.$route ? this.$route.query : null;
          if (query && query.id) {
            return;
          }

          const city = this.cityRes?.cityname;
          const res = await this.$api.accompanyDoctor.getAccompanyservicePage({
            current: 1,
            size: 10,
            condition: {
              classifyId: this.classifyId,
              state: 1,
              city: city,
              providerId: serverOptions.providerId
            },
            // 按orderValue排序
            ascs: 'orderValue'
          });

          if (res.data && res.data.records && res.data.records.length > 0) {
            // 如果有服务，选择第一个
            const firstService = res.data.records[0];
            // 添加分类信息
            firstService.classifyId = this.classifyId;
            firstService.classifyName = this.classifyName;
            // 直接更新父组件的currentServer
            this.$emit('selectServer', firstService);
          } else {
            // 如果该分类下没有服务，显示Toast提示
            const message = `${this.classifyName || '该分类'}暂无服务`;
            uni.showToast({
              title: message,
              icon: 'none',
              duration: 2000
            });

            // 创建一个空的服务对象，显示"未选择服务"
            const emptyService = {
              id: '',
              serviceName: '未选择服务',
              listImg: this.$static_ctx + 'image/business/accompany-doctor/no-data.png',
              price: 0,
              cityPrice: 0,
              classifyId: this.classifyId,
              classifyName: this.classifyName
            };
            this.$emit('selectServer', emptyService);
          }
        } catch (error) {
          // 错误时显示Toast提示
          uni.showToast({
            title: '获取服务列表失败，请稍后再试',
            icon: 'none',
            duration: 2000
          });

          // 创建一个空的服务对象，显示"未选择服务"
          const emptyService = {
            id: '',
            serviceName: '未选择服务',
            listImg: this.$static_ctx + 'image/business/accompany-doctor/no-data.png',
            price: 0,
            cityPrice: 0
          };
          this.$emit('selectServer', emptyService);
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .classify-name{
    width: 100%;
  }
  .required{
    font-weight: 500;
    font-size: 14px;
    color: #FF5500;
  }
  .registrationPrompts{
    width: 100%;
    height: 64rpx;
    background: #E0F4EF;
    border-radius: 8rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #00926B;
    padding-left: 20rpx;
    display: flex;
    align-items: center;
    margin-top: 24rpx;
    .warningGreen{
      width: 28rpx;
      height: 28rpx;
      margin-right: 12rpx;
    }
  }
  .noScroll{
    overflow: hidden !important;
  }
  .boxTab{
    width: 100vw;
    height: 1071rpx;
    overflow: scroll;
    box-sizing: border-box;
    padding: 0 32rpx;
    .headerTab{
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      padding: 24rpx;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .serverIcon{
        width: 144rpx;
        height: 144rpx;
        border-radius: 12rpx;
        border: 1rpx solid #D9DBE0;
        margin-right: 20rpx;
      }
      .serviceName{
        font-weight: 500;
        font-size: 32rpx;
        color: #1D2029;
        .signal{
          font-weight: 400;
          font-size: 22rpx;
          color: #FF5500;
        }
        .serverNum{
          font-weight: 500;
          font-size: 36rpx;
          color: #FF5500;
        }
        .tag{
          font-weight: 400;
          font-size: 20rpx;
          color: #868C9C;
        }
      }
      .changeServer{
        width: 148rpx;
        height: 52rpx;
        line-height: 52rpx;
        text-align: center;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 1rpx solid #D9DBE0;
        margin-left: auto;
      }

    }
    .botTab{
      box-sizing: border-box;
      padding: 0 32rpx 32rpx 32rpx;
      width: 686rpx;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin-top: 20rpx;
      margin-bottom: 50px;
      .lineHide{
        width: 0;
        overflow: hidden;
        height: 0;
      }
      .additionalContent{
        margin-top: 32rpx;
        padding-bottom: 10rpx;
        border-bottom: 2rpx solid #EAEBF0;
        .additionalContentImageUpMap{
          display: flex;
          flex-wrap: wrap;
        }
        .additionalContentImageUp{
          margin-top: 12rpx;
          width:  194rpx;
          height: 194rpx;
          background: #F4F6FA;
          border-radius: 16rpx;
          text-align: center;
          position: relative;
          margin-right: calc((100% - (194rpx * 3)) / 2);
          &:nth-of-type(3n){
            margin-right: 0;
          }
          .imageUpImage{
            width: 64rpx;
            height: 54rpx;
            margin-top: 46rpx;
          }
          .imageUpText{
            font-weight: 400;
            font-size: 24rpx;
            color: #4E5569;
          }
          .imageContent{
            width: 100%;
            height: 100%;
          }
          .iconPostMenuClose{
            width: 30rpx;
            height: 30rpx;
            position: absolute;
            top: 0;
            right: 0;
            background: white;
          }
        }
      }
      .lineTitle{
        font-weight: 500;
        font-size: 28rpx;
        color: #1D2029;
        width: 212rpx;
      }
      .textarea{
        flex-wrap: wrap;
        height: auto !important;
        .bookTextarea{
          width: 100%;
          line-height: 40rpx;
        }
      }
      .botTabLine{
        width: 100%;
        height: 104rpx;
        line-height: 104rpx;
        border-bottom: 2rpx solid #EAEBF0;
        display: flex;
        align-items: center;
        .lineValue{
          flex: 1;
        }
        .lineIcon{
          width: 32rpx;
          height: 32rpx;
        }
        .bookInput{
          width: 438rpx;
          height: 44rpx;
          line-height: 44rpx;
        }
        .botTabLine{
          width: 438rpx;
          height: 40rpx;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 28rpx;
          color: #A5AAB8;
        }
      }
    }
    .bottomBtn{
      width: 750rpx;
      padding-bottom: 25rpx;
      background: #FFFFFF;
      box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
      position: fixed;
      bottom: 0;
      left: 0;
      .comBtn{
        width: 686rpx;
        height: 88rpx;
        background: #00B484;
        border-radius: 44rpx;
        text-align: center;
        line-height: 88rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #FFFFFF;
        margin: 24rpx auto;

        &.disabled {
          background: #cccccc;
          opacity: 0.8;
          pointer-events: none;
        }
      }
    }
  }





    .patient-select-input {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 16rpx;

      .patient-name-text {
        flex: 1;
        color: #333;
        font-size: 28rpx;
      }

      .select-arrow {
        width: 32rpx;
        height: 32rpx;
      }
    }
</style>
