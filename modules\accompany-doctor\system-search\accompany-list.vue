<template>
  <view >
    <!-- 搜索框 -->
  <view class="top-nav">
    <view class="top-nav-body">
      <!-- <em class="icon-back-left" @tap="$navto.back(1)"></em> -->
      <view class="input-view">
        <i class="icon-positioning-search"></i>
        <input confirm-type="search" placeholder="搜索陪诊师姓名" placeholder-style="color: #BFBFBF" class="input" type="text" :value="search" @input="searchInputFn" @confirm="searchFn">
        <view class="line"></view>
        <view class="click" @tap="searchFn">
          搜索
        </view>
      </view>
    </view>
  </view>
  <!-- tab -->
  <view class="tabBox">
    <view class="tabItem" @click="selectTab(item)" :style="{width:100 / tabs.length + '%'}" v-for="(item,index) in tabs" :key="index">
      {{item.name}}
      <image class="triangle" :src='triangle'></image>
    </view>
  </view>
    <view class="m-main-body">
      <scroll-refresh bgColor='white' class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
        <view class="depa-doctor-item" v-for="item in indexlist" :key="item.id" @click="handleJump(item.id)"
        >
          <view class="doctor-item-box">
            <view class="doctor-item-l">
              <image class="idCardHeader-r" mode="aspectFit" :src="item.avatar"></image>
              <!--<image v-if="item.sex == 0" mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-list-avatar-default-grild.png'"></image>
              <image v-else mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-list-avatar-default-man.png'"></image>-->
            </view>
            <view class="doctor-item-r">
              <view class="item-r-head">
                <view class="name">{{ item.username }}<image class="pzsNameLogo" :src="pzsNameLogo"></image></view>
                <view class="star" v-if="item.star">
                  <image class="starIcon" :src="star" mode=""></image>
                  {{ item.star }}
                </view>
              </view>
              <view class="item-r-bott">
                {{ item.experience }}
              </view>
              <view class="languageMap" v-if="item.language && item.language.split(',').filter(l => l).length > 0">
                <view class="languageItem" :key="index" v-for="(languageItem,index) in sortLanguages(item.language.split(',').filter(l => l))">{{languageItem}}</view>
              </view>
              <view class="line"></view>
            </view>
          </view>
        </view>
      </scroll-refresh>
    </view>
    <!-- 城市套餐 -->
    <view class="lineHide">
      <dataPicker ref="selectList" :localdata="getSelectOtions" :popup-title="selectTitle" @change="onchangeCity" :value="selectedCity"></dataPicker>
    </view>
    <view class="lineHide">
      <selectData ref="selectLanguage" :isMore='true' :hiddenSearch='false' :localdata="getSelectOtions" :popup-title="selectTitle" @change="onchangeLanguage" :value="selectedLanguage">
        <template #header>
          <view class="select-all-header">
            <view class="select-all-btn" @tap="selectAllLanguage">全选</view>
            <view class="select-all-btn" @tap="unselectAllLanguage">取消全选</view>
          </view>
        </template>
      </selectData>
    </view>
  </view>
</template>

<script>
  import { isDomainUrl } from '@/utils/index.js'
  import serverOptions from '@/config/env/options'
  import dataPicker from '../components/uni-data-picker/uni-data-picker.vue'
  import selectData from '../components/select-data.vue'
  export default {
    components: {
      dataPicker,
      selectData
    },
    computed:{
      getSelectOtions(){
        if(this.tabId === 'city') return this.accompanyproviderList
        if(this.tabId === 'language') return this.languageList
      }
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        departmentsList:[],
        doctorList:[],
        currentActive:0,
        depaList:[],
        depaActive:0,
        hospitalId:'',
        deptId:null,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        moreFlag:false,
        icnosType:'bottom',
        curIndex:0,
        tabs: [{ name: '城市',id:'city',ref:'selectList'},{ name: '语言',id:'language',ref:'selectLanguage'}],
        tabId:'',
        selectTitle:'',
        accompanyMap:[],
        cityName:'',
        language:'',
        triangle: this.file_ctx + 'static/image/business/hulu-v2/triangle.png',
        star: this.file_ctx + 'static/image/business/hulu-v2/star.png',
        accompanyproviderList:[],
        languageList:[
          { value: '普通话', text: '普通话', isSelect: false },
          { value: '粤语', text:'粤语', isSelect: false},
          { value: '英语', text:'英语', isSelect: false},
          { value: '客家话', text:'客家话', isSelect: false},
          { value: '潮汕话', text:'潮汕话', isSelect: false},
          { value: '五邑话', text:'五邑话', isSelect: false},
          { value: '高州话', text:'高州话', isSelect: false},
          { value: '湖南方言', text:'湖南方言', isSelect: false},
          { value: '海南话', text:'海南话', isSelect: false},
          { value: '四川话', text:'四川话', isSelect: false},
          { value: '东北话', text:'东北话', isSelect: false},
          { value: '河南话', text:'河南话', isSelect: false},
        ],
        pzsNameLogo: this.file_ctx + 'static/image/business/hulu-v2/pzsNameLogo.png',
        selectedCity: '', // 保存选中的城市值
        selectedLanguage: [], // 保存选中的语言值数组
      }
    },
    async onLoad(option){
      console.log('option',option);
      if(option.cityName){
        this.cityName = decodeURIComponent(option.cityName)
      }
      await this.accompanyproviderQueryPage();
      this.init()
    },
    methods:{
      searchInputFn({detail:{value}}){
        this.username = value;
        this.init()
      },
      query(){

        // this.$api.accompanyDoctor.accompanyemployeeQueryStarPage()
      },
      onchangeCity({detail:{value}}){
        let [,{value:city}] = value
        console.log('city',city);
        this.selectedCity = value; // 保存选中的值用于回显
        this.cityName = city;
        this.init()
      },
      onchangeLanguage({detail:{value}}){
        // 从 value[0] 获取语言数组
        this.selectedLanguage = value[0] || [];
        this.language = this.selectedLanguage.join(',')
        console.log('language',this.language);

        // 更新语言列表的选中状态
        this.languageList.forEach(item => {
          item.isSelect = this.selectedLanguage.includes(item.value);
        });

        this.init()
        this.$nextTick(()=>{
          this.$refs.selectLanguage.close()
        })
      },
      selectTab({id,name,ref}){
        this.tabId = id
        this.selectTitle = `请选择${name}`
        this.$refs[ref].show()
      },
      async accompanyproviderQueryPage(){
        // 判断当前是否是平台
        let queryOptions;
        if(serverOptions.source === 1){
          queryOptions = (await this.$api.accompanyDoctor.getAccompanyproviderAll()).data
        }else{
          queryOptions = [(await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data]
        }
        let cityMap = this.getCityMap(queryOptions)
        this.accompanyproviderList = [...new Set(cityMap)].filter(e=>e);
      },
      getCityMap(AccompanyproviderAll){
          return AccompanyproviderAll.reduce((acc, {province, city}) => {
              let provinceMap = province.split(',');
              let cityMap = city.split('$');
              provinceMap.map((provinceItem,index)=>{
              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap)
              })
              return acc;
          }, []).map(e=>{
              e.children = [...new Set(e.children)];
              e.children = e.children.map(e=>({text:e,value:e}))
              return e;
          });
      },
      returnFn(obj) {
        console.log('this.mescroll3',this.mescroll);
        const that = this
        setTimeout(()=> {
          const xiaohuluProviderId = '2124021789005144070'; // 小葫芦陪诊的 providerId
          const isXiaoHuLu = serverOptions.providerId === xiaohuluProviderId;

          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              city: this.cityName,
              username:this.username,
              language:this.language || null,
              auditStatus: 2
            }
          }

          if (isXiaoHuLu) {
            // 如果是小葫芦陪诊，添加 omitProviderId 标记
            params.omitProviderId = true;
          }
          let defaultUrl = '0/msg-reply/1013082175633223682.png';
          that.$api.accompanyDoctor.accompanyemployeeQueryStarPage(params).then(res => {
              let data = res.data.records.map(item=>({...item,avatar:isDomainUrl(item.avatar || defaultUrl)})) || []
              if (obj.pageNum === 1) {
                  that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
          }).catch(err => {
              console.error("获取陪诊师列表失败 (accompany-list):", err);
              if (obj.pageNum === 1) {
                  that.indexlist = [];
              }
          });
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
        console.log('this.mescroll1',this.mescroll);
      },

      init() {
        this.$nextTick(() => {
          console.log('this.mescroll2',this.mescroll);
          this.mescroll.triggerDownScroll()
        })
      },
      handleJump(id){
        this.$navto.push('accompanyDoctorDetails', {id})
      },
      sortLanguages(languages) {
        // 定义语言优先级顺序：1.英语 2.粤语 3.普通话
        const languagePriority = {
          '英语': 1,
          '粤语': 2,
          '普通话': 3
        };

        // 对语言数组进行排序
        return [...languages].sort((a, b) => {
          const priorityA = languagePriority[a] || 999;
          const priorityB = languagePriority[b] || 999;
          return priorityA - priorityB;
        });
      },
      searchFn(){
        this.init()
      },
      selectAllLanguage() {
        // 全选所有语言
        this.selectedLanguage = this.languageList.map(item => item.value);
        this.language = this.selectedLanguage.join(',');

        // 更新所有语言项为选中状态
        this.languageList.forEach(item => {
          item.isSelect = true;
        });

        // 刷新数据列表
        this.init();
        this.$nextTick(()=>{
          this.$refs.selectLanguage.close();
        });
      },
      unselectAllLanguage() {
        // 取消全选
        this.selectedLanguage = [];
        this.language = '';

        // 更新所有语言项为未选中状态
        this.languageList.forEach(item => {
          item.isSelect = false;
        });

        // 刷新数据列表
        this.init();
        this.$nextTick(()=>{
          this.$refs.selectLanguage.close();
        });
      },
    },
 }
</script>

<style lang='scss' scoped>
.lineHide{
  width: 0;
  overflow: hidden;
  height: 0;
}
.select-all-header {
  display: flex;
  justify-content: flex-end;
  padding: 20rpx;
  border-bottom: 2rpx solid #F2F3F7;
  box-sizing: border-box;
  --header-height: 80rpx; /* 设置CSS变量给组件使用 */

  .select-all-btn {
    padding: 10rpx 20rpx;
    margin-left: 20rpx;
    background: #00B484;
    border-radius: 6rpx;
    font-size: 28rpx;
    color: #FFFFFF;
  }
}
  .tabBox{
    width: 100%;
    padding: 30rpx 0;
    display: flex;
    background-color: white;
    .tabItem{
      text-align: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #1D2029;
      display: flex;
      justify-content: center;
      align-items: center;
      .triangle{
        width: 16rpx;
        height: 10rpx;
        margin-left: 4rpx;
      }
    }
  }
  .top-nav{
    padding: 10upx 30upx 10upx 30upx;
    background-color: #fff;
    .top-nav-body{
      display: flex;
      align-items: center;
      .icon-back-left{
        display: inline-block;
        vertical-align: middle;
        @include iconImg(64, 64, '/system/icon-back-left.png');
        margin-right: 16upx;
      }
      .input-view {
        flex: 1;
        display: flex;
        align-items: center;
        width: calc(100% - 220upx);
        @include rounded(38upx);
        line-height: 64upx;
        height: 64upx;
        padding: 0 20upx;
        background: #FFFFFF;
        border-radius: 36rpx;
        border: 2rpx solid #D9DBE0;
        .icon-positioning-search{
          display: inline-block;
          vertical-align: middle;
          margin-right: 6upx;
          @include iconImg(32, 32, '/system/icon-positioning-search.png');
        }
        .input {
          width: calc(100% - 78upx);
          display: inline-block;
          vertical-align: middle;
          font-size: 28upx;
          line-height: 42upx;
          color: #333;
        }
      }
      .line{
        width: 2rpx;
        height: 100rpx;
        background: #DBDDE0;
      }
      .click{
        display: inline-block;
        vertical-align: middle;
        text-align: right;
        width: 100upx;
        line-height: 64upx;
        height: 64upx;
      }
    }
  }
// .line{
//     display: block;
//     width: 100%;
//     height: 1upx;
//     background-color: #e0e0e0;
//     transform: scaleY(.333);
//     position: absolute;
//     bottom: 0;
// }
  .languageMap{
    display: flex;
    margin-top: 8rpx;
    flex-wrap: wrap;
    .languageItem{
      height: 36rpx;
      padding: 2rpx 8rpx;
      margin-right: 8rpx;
      margin-bottom: 8rpx;
      background: #EBF7F5;
      border-radius: 4rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #007959;
    }
  }
  .m-main-body{
    height: calc(100vh - 192rpx);
    background-color: white;
    .scroll-refresh-main{
      height: 100%;
      background-color: white;
      ::v-deep .mescroll-uni{
        .z-paging-content{
          // background-color: #fff !important;
          // background-color: pink !important;
        }
      }
      .depa-doctor-item{
        // padding:20upx 0upx;
        margin: 0 20rpx 20rpx;
        background-color: #fff;
        .doctor-item-box{
          display: flex;
          // padding-bottom:20upx;
          .doctor-item-l{
            width: 180rpx;
            height: 180rpx;
            // background: #D5F0E9;
            border-radius: 9rpx;
            image{
              width: 100%;
              height: 100%;
            }
          }
            .doctor-item-r{
              display: flex;
              flex-direction: column;
              flex: 1;
              margin-left: 24rpx;
              position: relative;
              height: 180rpx;
              // padding-bottom: 26rpx;
              // height: 200rpx;
              padding-bottom: 20rpx;
              border-bottom: 2rpx solid #F2F3F7;
              // box-sizing: border-box;
              .item-r-head{
                display: flex;
                align-items: center;
                .star{
                  margin-left: 16rpx;
                  padding: 0 4rpx;
                  height: 32rpx;
                  background: #00B484;
                  border-radius: 4rpx;
                  display: flex;
                  align-items: center;
                  font-weight: 600;
                  font-size: 22rpx;
                  color: #FFFFFF;
                  .starIcon{
                    width: 20rpx;
                    height: 20rpx;
                    margin-right: 2rpx;

                  }
                }
                .name{
                  display: flex;
                  align-items: center;
                  font-weight: 500;
                  font-size: 32rpx;
                  color: #1D2029;
                  .pzsNameLogo{
                    display: flex;
                    width: 36rpx;
                    height: 34rpx;
                  }
                }
                span{
                  margin-left: 10upx;
                }
              }
              .item-r-bott{
                margin-top: 4rpx;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                font-weight: 400;
                font-size: 24rpx;
                color: #4E5569;
              }
            }
        }
        &:last-child{
          margin-bottom: 0;
        }
      }
      .header{
          display: flex;
          align-items: center;
          padding:30upx 20upx;
          margin:0 20upx;
          border-radius: 20upx;
          background-color: #fff;
          .header-l{
              width: 80upx;
              height: 80upx;
              image{
                  width: 100%;
                  height: 100%;
              }
          }
          .header-r{
              margin-left: 10upx;
              .title{
                  font-size: 32upx;
                  font-weight: bold;
                  color:#000;
              }
          }

      }
      .hospital-content{
          padding:20upx;
          margin:20upx 20upx 0;
          background-color: #fff;
          border-radius: 20upx;
          .hospital-head{
              padding:20upx 0;
              h3{
                  color:#000;
                  font-size: 32upx;
                  font-weight: bold;
              }
              .info{
                  padding-top: 15upx;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 3;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  color:#333;
              }
          }
          .outpatient{
              .info{
                  padding:25upx 0;
                  color:#333;
                  span{
                      display: inline-block;
                      color: rgb(253, 80, 62);
                      background-color: rgba(253, 80, 62, 0.1);
                      padding:0 10upx;
                      border-radius: 10upx;
                      margin-right: 10upx;
                  }
              }
          }
          .outpatient-date{
              .info{
                  padding:25upx 0;
                  color:#333;
              }
          }
          .hospital-phone{
              .info{
                  padding:25upx 0;
                  color:#333;
              }
          }
          .hospital-address{
              padding-top:25upx;
              color:#333;
          }
      }
    .accompany-bott{
      display: flex;
      padding:40rpx 0;
      background: #FFFFFF;
      border-radius: 16rpx;
      margin: 20rpx 20rpx 0;
      .accompany-bott-item{
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        text-align: center;
        // justify-content: center;
        .img{
          width: 72rpx;
          height: 72rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        .title{
          font-size: 24rpx;
          color: #1D2029;
          margin-top: 12rpx;
          width: 120rpx;
          word-wrap: break-word;
          white-space: normal;
          word-break: break-all;
        }
      }
    }
      .hospital-depa{
          margin:20upx 20upx 0;
          border-radius: 20upx 20upx 0 0;
          padding:20upx;
          background-color: #fff;
          // height: 100%;
          .depa-header{
              display: flex;
              width: 100%;
              ::v-deep.mytab{
                width: 100%;
                .tabs-sticky{
                  width: 100%;
                  .tabs-sticky-body{
                    .tab{
                      display: flex;
                      flex: 1;
                      .text-width{
                        width: 100%;
                      }
                    }
                  }
                }
              }
          }
          .depa-title{
              display: flex;
              justify-content: space-between;
              margin:30upx 0 30upx;
              .title-l{
                  color:#333;
              }
              .title-r{
                  color:#000;
                  font-size: 26upx;
              }
          }
          .depa-tag-list{
              .depa-tag-box{
                  display: flex;
                  flex-wrap: wrap;
                  max-height:182upx;
                  overflow: hidden;
                  .depa-tag-item{
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      padding:4upx 7upx;
                      border-radius: 15upx;
                      border:1px solid #8f939c;
                      margin-right: 15upx;
                      margin-bottom: 15upx;
                      overflow: hidden;
                      color:#8f939c;
                  }
                  .active{
                      border-color: #2979ff;
                      color:#2979ff;
                  }
              }
              &::-webkit-scrollbar{
                  height: 0;
              }
              .box-active{
                  max-height:360upx;
                  overflow: auto;
              }
          }
          .more{
            display: block;
            height: 40upx;
            line-height: 40upx;
            width: calc(100% - 40upx);
            font-size: 28upx;
            text-align: center;
            margin: 15upx 20upx 0;
            border-radius: 20upx;
            color: #333;
            .move-active{
              margin-left: 10upx;
            }
          }
      }
    }
  }
</style>
