<template>
  <view class="main" :class="'order-' + skinColor">
    <view :style="'height:' + statusBarHeight + 'px;'"></view>
    <view class="top-nav">
      <view class="top-nav-l" @click.stop="handleBack"><image mode="aspectFit" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-service-left-arrows.png'" class="header-search-img"/></view>
      <view class="top-nav-c">订单</view>
    </view>
      <view class="l-main">
        <view class="tab" v-for="(tab, i) in tabs" :key="i" @click="changeTab(i)">
          <text :class="tabIndex == i ?'text-default active':'text-default'">{{tab.name}}</text>
          <view v-if="tabIndex == i" class="text-width" :style="{'background-image':'url('+ file_ctx + 'static/image/business/hulu-v2/border-bottom.png)','background-size': '100%'}"></view>
        </view>
      </view>
    <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
      <view class="order-list">
        <view class="order-item" v-for="item in contentList" :key="item.id" @click="gotoServiceReservation(item)">
          <view class="order-item-box">
            <view class="order-item-t">
              <view class="order-item-t-l">{{ item.serviceName }}</view>
              <view
                class="order-item-t-r"
                :style="{color:item.orderState == 1 ? '#1687F7' : item.orderState == 2 ? '#FF5500' : (item.orderState == 5 || item.orderState == 6) ? '#00B484' : item.orderState == 7 ? '#1D2029' : '#868C9C'}">
                  {{ handleFilterOrderState(item.orderState) }}
              </view>
            </view>
            <view class="order-item-b">
              <view class="cost" v-if="item.payPrice">服务费用：<span>￥{{ item.payPrice / 100 }}</span></view>
              <view class="time">陪诊时间：<span>{{ item.startTime }}<span v-if="item.endTime">~</span>{{ item.endTime }}</span></view>
              <view class="hospital" v-if="item.hospitalName">就诊医院：<span>{{ item.hospitalName }}</span></view>
              <view class="hospital">订单号：<span>{{ item.id }}</span></view>
              <view class="accompany-teacher" v-if="item.employeeId">陪诊师：<view class="teacher-box"><view class="img"><image :src="file_ctx + item.avatar"></image></view> <span>{{ item.employeeName }}</span></view></view>
              <button class="evaluate" v-if="item.orderState === 7 && item.commentState !== 2">前往评价</button>
              <view class="my-evaluate" v-if="item.commentState == 2">
                我的评价：
                <view class="evaluate-img1" v-if="item.star == 1"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate1.png'"></image></view>
                <view class="evaluate-img2" v-if="item.star == 2"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate2.png'"></image></view>
                <view class="evaluate-img3" v-if="item.star == 3"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate3.png'"></image></view>
                <view class="evaluate-img4" v-if="item.star == 4"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate4.png'"></image></view>
                <view class="evaluate-img5" v-if="item.star == 5"><image class="img" :src="file_ctx + 'static/image/business/accompany-doctor/icon-accompany-very-evaluate5.png'"></image></view>
              </view>
            </view>
          </view>
          <!-- 保险提示模块 -->
          <view class="insuranceTips" :class="{inInsuranceTips:!item.notPurchased}" v-if="showInsuranceTips(item.orderState,item.insure,item.refundInsure)">
            <view class="insuranceContent">门诊无忧意外保障-基础保障</view>
            <view class="purchased">
              <image class="baox" :src="baox" mode=""></image>
              保障中
            </view>
          </view>
        </view>

      </view>
    </scroll-refresh>
    <view class="isNoLogin" v-if="!isLogin" @click="gotoLogin">登陆查看更多</view>
  </view>
</template>

<script>
  import TabsSticky from '@/components/basics/tabs-sticky-v3'
  import { mapState } from 'vuex'
  export default {
    components:{
      TabsSticky,
    },
    data(){
      return{
        file_ctx: this.file_ctx,
        curIndex:0,
        statusBarHeight: 0,
        tabs:[{name:'全部',id:null},{name:'待接入',id:1},{name:'待支付',id:2},{name:'待派单',id:3},{name:'待接单',id:4},{name:'待服务',id:5},{name:'服务中',id:6},{name:'已完成',id:7},{name:'已取消',id:8}],
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          }
        },
        contentList:[],
        orderState:null,
        right_o: this.file_ctx + 'static/image/business/hulu-v2/right-o.png',
        baox: this.file_ctx + 'static/image/business/hulu-v2/baox.png',
        tabIndex:0,
      }
    },
    computed: {
      ...mapState('user', {
        curSelectUserInfo: state => state.curSelectUserInfo,
        isLogin: state => state.isLogin
      }),
    },
    onLoad(){},
    onShow(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    mounted(){
      this.$nextTick(() => {
        this.init()
      })
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight // 获取设备的状态栏高度
    },
    methods:{
      gotoServiceReservation({id,combineOrderId,orderState}){
        console.log('combineOrderId',combineOrderId,id);
        // if(combineOrderId && orderState == 2){
        //   return this.$navto.push('joinOrder',{orderId:combineOrderId})
        // }
        this.$navto.push('serviceReservation',{orderId:id})
      },
      showInsuranceTips(type,insure,refundInsure){
        if(refundInsure) return false
        let showList = [3,4,5,6];
        if(showList.indexOf(type) >= 0 && insure) return true
        return false
      },
      handleFilterOrderState(type){
        switch(type){
          case 1:
            return '等待人工导诊'
          case 2:
            return '待支付'
          case 3:
            return '待派单'
          case 4:
            return '待接单'
          case 5:
            return '待服务'
          case 6:
            return '服务中'
          case 7:
            return '已完成'
          case 8:
            return '已取消'
        }
      },
      handleBack(){
        this.$navto.back(1)
      },
      changeTab(index) {
        this.tabIndex = index
        this.orderState = this.tabs[index].id
        this.init()
      },
      // 查询购买人信息
      async loadAccompanyinsure(id){
        let {data} = await this.$api.accompanyDoctor.accompanyinsureQuery({accompanyId:id})
        if(!data) return {}
        let { refundInsure } = data;
        refundInsure = (refundInsure === 0 || refundInsure === '') ? 0 : 1;
        return {refundInsure}
      },
      returnFn(obj) {
        const that = this
        const providerId = this.$common.getKeyVal('user','providerId',true)
        setTimeout(function () {
          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: {
              orderState:that.orderState,
              providerId:providerId,
            }
          }
          that.$api.accompanyDoctor.accompanybookQueryMiniAppPage(params).then(async res => {
            // let data = res.data.records.map(item=>({...item,listCover:isDomainUrl(item.listCover)}))
            let dataFnMap = res.data.records.map(async item=>(
              {
                ...item,
                ...await that.loadAccompanyinsure(item.id),
                startTime:that.$common.formatDate(new Date(item.startTime), 'yyyy-MM-dd').replace(/-/g, '.'),
                endTime: item.endTime ? that.$common.formatDate(new Date(item.endTime), 'yyyy-MM-dd').replace(/-/g, '.') : '',
              }
            ))
            let data = await Promise.all(dataFnMap)
            console.log('datadatadata',data);
            if (obj.pageNum === 1) {
              that.contentList = []
            }
            that.contentList = [...that.contentList, ...data]
            obj.successCallback && obj.successCallback(data)
          }).catch(error => {
            if(error.msg == '账号信息不能为空'){
              uni.showToast({
                title: '请先登录',
                icon:'none'
              });
            }
            obj.successCallback && obj.successCallback([])
            return false
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
      },
      init() {
        this.$nextTick(() => {
          this.mescroll.triggerDownScroll()
        })
      },
    },
 }
</script>

<style lang='scss' scoped>
@import '../style/blueSkin.scss';

  .main{
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    background-color: #fff;
  }
  .top-nav{
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .l-main{
    position: sticky;
    top: 0;
    z-index: 999;
    padding: 22rpx 32rpx 22rpx 32rpx;
    display: flex;
    overflow: scroll;
    .tab{
        display: inline-block;
        position: relative;
        vertical-align: middle;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 48rpx;
        .text-default{
          height: 50rpx;
          line-height: 50rpx;
          font-family: '思源黑体';
          font-size: 32rpx;
          color: #1D2029;
          white-space: nowrap;
        }
        .active{
          font-size: 36rpx;
          font-weight: 600;
          color: #00B484;
        }
        .text-width{
          display: flex;
          width: 38rpx;
          height: 10rpx;
          image{
            width: 100%;
            height: 100%;
          }
        }
        &:last-child{
          margin-right: 0;
        }
      }
  }
  .img{
    width: 100%;
    height: 100%;
  }
  .scroll-refresh-main{
    flex: 1;
    // height: calc(100% - 60rpx);
    overflow-x: hidden;
    // padding-bottom: 166rpx;
    background-color: #F4F6FA;
    ::v-deep .mescroll-empty-box{
      // min-height: 0%;
      position: absolute !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .order-list{
      overflow-x: hidden;
      padding: 0 32rpx;
      .order-item{
        margin-top: 20rpx;
        background-color: #fff;
        border-radius: 16rpx;
        position: relative;
        .order-item-box{
          padding:32rpx 24rpx;
        }
        .inInsuranceTips{
          background: #E0F4EF !important;
        }
        .insuranceTips{
          width: 100%;
          height: 72rpx;
          background: #FFEBCB;
          padding: 20rpx 24rpx;
          box-sizing: border-box;
          border-radius: 0rpx 0rpx 16rpx 16rpx;
          font-weight: 400;
          font-size: 22rpx;
          display: flex;
          justify-content: space-between;
          .insuranceContent{
            color: #777777;
            font-weight: 400;
            font-size: 22rpx;
            color: #777777;
          }
          .notPurchased{
            font-weight: 500;
            font-size: 22rpx;
            color: #FFB130;
            display: flex;
            justify-content: center;
            .right_o{
              width: 32rpx;
              height: 32rpx;
              margin-right: 12rpx;
            }

          }
          .purchased{
            font-weight: 500;
            font-size: 22rpx;
            color: #00B484;
            display: flex;
            justify-content: center;
            .baox{
              width: 32rpx;
              height: 32rpx;
              margin-left: 12rpx;
            }
          }
        }
        .order-item-t{
          display: flex;
          justify-content: space-between;
          .order-item-t-l{
            font-size: 32rpx;
            color: #1D2029;
            line-height: 44rpx;
          }
          .order-item-t-r{
            font-size: 28rpx;
            color: #1687F7;
            line-height: 40rpx;
          }
        }
        .order-item-b{
          margin-top: 16rpx;
          .cost,.time,.hospital,.accompany-teacher{
            font-size: 24rpx;
            color: #4E5569;
            line-height: 34rpx;
            span{
              color: #1D2029;
            }
          }
          .time{
            margin: 8rpx 0;
          }
          .hospital{}
          .accompany-teacher{
            display: flex;
            .teacher-box{
              display: flex;
              align-items: center;
              .img{
                width: 32rpx;
                height: 32rpx;
                border-radius: 50%;
                image{
                  width: 100%;
                  height: 100%;
                }
              }
              span{
                margin-left: 8rpx;
                font-size: 22rpx;
                color: #1D2029;
                line-height: 32rpx;
              }
            }
          }
          .evaluate{
            display: flex;
            align-items: center;
            justify-content: center;
            height: 72rpx;
            width: 100%;
            margin-top: 24rpx;
            font-size: 26rpx;
            color: #00B484;
            background: #FFFFFF;
            border-radius: 36rpx;
            border: 1rpx solid #00B484;
            &::after{
              border: none !important;
            }
          }
          .my-evaluate{
            display: flex;
            align-items: center;
            height: 72rpx;
            background: #F4F6FA;
            border-radius: 8rpx;
            padding: 18rpx 16rpx 20rpx;
            box-sizing: border-box;
            margin-top: 24rpx;
            // .evaluate-img{
            //   display: flex;
            //   width: 132rpx;
            //   height: 32rpx;
            // }
            .evaluate-img1{
              width: 204rpx;
              height: 48rpx;
            }
            .evaluate-img2{
              width: 156rpx;
              height: 48rpx;
            }
            .evaluate-img3{
              width: 132rpx;
              height: 48rpx;
            }
            .evaluate-img4{
              width: 132rpx;
              height: 48rpx;
            }
            .evaluate-img5{
              width: 180rpx;
              height: 48rpx;
            }
          }
        }
      }
    }
  }
</style>
